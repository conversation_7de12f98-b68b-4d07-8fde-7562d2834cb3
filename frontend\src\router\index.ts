import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/LoginView.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideNavigation: true
    }
  },
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/HomeView.vue'),
    meta: {
      title: '首页',
      requiresAuth: true
    }
  },
  {
    path: '/products',
    redirect: '/settings/products'
  },
  // 兼容性重定向路由
  {
    path: '/settings/suppliers',
    redirect: '/purchase/suppliers'
  },
  {
    path: '/settings/customers',
    redirect: '/sales/customers'
  },
  {
    path: '/inventory/receipt',
    redirect: '/purchase/receipt'
  },
  {
    path: '/inventory/outbound',
    redirect: '/sales/outbound'
  },
  {
    path: '/settings/products',
    name: 'ProductArchive',
    component: () => import('../views/ProductArchiveView.vue'),
    meta: {
      title: '商品档案',
      requiresAuth: true
    }
  },
  {
    path: '/settings/platform-apis',
    name: 'PlatformAPI',
    component: () => import('../views/PlatformAPIView.vue'),
    meta: {
      title: '电商平台API管理',
      requiresAuth: true
    }
  },
  {
    path: '/inventory/query',
    name: 'InventoryQuery',
    component: () => import('../views/InventoryQueryView.vue'),
    meta: {
      title: '库存查询',
      requiresAuth: true
    }
  },
  {
    path: '/inventory/transfers',
    name: 'InventoryTransfer',
    component: () => import('../views/InventoryTransferView.vue'),
    meta: {
      title: '库存调拨单',
      requiresAuth: true
    }
  },

  {
    path: '/warehouses',
    name: 'Warehouse',
    component: () => import('../views/WarehouseView.vue'),
    meta: {
      title: '仓库管理',
      requiresAuth: true
    }
  },
  {
    path: '/purchase/orders',
    name: 'PurchaseOrder',
    component: () => import('../views/PurchaseOrderView.vue'),
    meta: {
      title: '采购订单',
      requiresAuth: true
    }
  },
  {
    path: '/purchase/receipt',
    name: 'PurchaseReceipt',
    component: () => import('../views/PurchaseReceiptView.vue'),
    meta: {
      title: '采购入库单',
      requiresAuth: true
    }
  },
  {
    path: '/purchase/returns',
    name: 'PurchaseReturn',
    component: () => import('../views/PurchaseReturnView.vue'),
    meta: {
      title: '采购退货单',
      requiresAuth: true
    }
  },
  {
    path: '/purchase/suppliers',
    name: 'SupplierArchive',
    component: () => import('../views/SupplierArchiveView.vue'),
    meta: {
      title: '供应商档案',
      requiresAuth: true
    }
  },
  {
    path: '/sales/orders',
    name: 'SalesOrder',
    component: () => import('../views/SalesOrderView.vue'),
    meta: {
      title: '销售订单',
      requiresAuth: true
    }
  },
  {
    path: '/sales/outbound',
    name: 'SalesOutbound',
    component: () => import('../views/SalesOutboundView.vue'),
    meta: {
      title: '销售出库单',
      requiresAuth: true
    }
  },
  {
    path: '/sales/returns',
    name: 'SalesReturn',
    component: () => import('../views/SalesReturnView.vue'),
    meta: {
      title: '销售退货单',
      requiresAuth: true
    }
  },
  {
    path: '/sales/customers',
    name: 'CustomerArchive',
    component: () => import('../views/CustomerArchiveView.vue'),
    meta: {
      title: '客户档案',
      requiresAuth: true
    }
  },
  {
    path: '/logistics',
    name: 'Logistics',
    component: () => import('../views/LogisticsView.vue'),
    meta: {
      title: '物流配送',
      requiresAuth: true
    }
  },
  {
    path: '/logistics/routes',
    name: 'LogisticsRoutes',
    component: () => import('../views/LogisticsRoutesView.vue'),
    meta: {
      title: '配送路线',
      requiresAuth: true
    }
  },
  {
    path: '/logistics/orders',
    name: 'DeliveryOrders',
    component: () => import('../views/DeliveryOrdersView.vue'),
    meta: {
      title: '配送订单',
      requiresAuth: true
    }
  },
  {
    path: '/logistics/cost-calculator',
    name: 'CostCalculator',
    component: () => import('../views/CostCalculatorView.vue'),
    meta: {
      title: '成本计算',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/ProfileView.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFoundView.vue'),
    meta: {
      title: '页面未找到',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态
  if (!authStore.user && localStorage.getItem('token')) {
    authStore.initializeAuth()
  }

  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 电子商务决策系统`
  }

  // 检查是否需要认证
  if (to.meta?.requiresAuth && !authStore.isLoggedIn) {
    // 需要登录但未登录，跳转到登录页
    next({
      name: 'Login',
      query: { redirect: to.fullPath }
    })
  } else if (to.name === 'Login' && authStore.isLoggedIn) {
    // 已登录用户访问登录页，跳转到首页
    next({ name: 'Home' })
  } else {
    next()
  }
})

export default router