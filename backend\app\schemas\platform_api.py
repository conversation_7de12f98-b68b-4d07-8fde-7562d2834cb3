"""
电商平台API配置的Pydantic模式
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.platform_api import APIType, PlatformType
import json


class PlatformAPIBase(BaseModel):
    """电商平台API配置基础模式"""
    platform_type: PlatformType = Field(..., description="平台类型")
    platform_name: str = Field(..., min_length=1, max_length=100, description="平台名称")
    api_type: APIType = Field(..., description="API类型")
    api_name: str = Field(..., min_length=1, max_length=100, description="API名称")
    api_url: str = Field(..., min_length=1, max_length=500, description="API地址")
    api_method: str = Field(default="GET", description="请求方法")
    api_headers: Optional[str] = Field(None, description="请求头配置(JSON格式)")
    api_params: Optional[str] = Field(None, description="请求参数配置(JSON格式)")
    api_auth: Optional[str] = Field(None, description="认证配置(JSON格式)")
    description: Optional[str] = Field(None, description="API描述")
    is_active: bool = Field(default=True, description="是否启用")

    @validator('api_method')
    def validate_api_method(cls, v):
        """验证API方法"""
        allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
        if v.upper() not in allowed_methods:
            raise ValueError(f'API方法必须是以下之一: {", ".join(allowed_methods)}')
        return v.upper()

    @validator('api_headers', 'api_params', 'api_auth')
    def validate_json_fields(cls, v):
        """验证JSON格式字段"""
        if v is not None and v.strip():
            try:
                json.loads(v)
            except json.JSONDecodeError:
                raise ValueError('必须是有效的JSON格式')
        return v


class PlatformAPICreate(PlatformAPIBase):
    """创建电商平台API配置的模式"""
    pass


class PlatformAPIUpdate(BaseModel):
    """更新电商平台API配置的模式"""
    platform_type: Optional[PlatformType] = None
    platform_name: Optional[str] = Field(None, min_length=1, max_length=100)
    api_type: Optional[APIType] = None
    api_name: Optional[str] = Field(None, min_length=1, max_length=100)
    api_url: Optional[str] = Field(None, min_length=1, max_length=500)
    api_method: Optional[str] = None
    api_headers: Optional[str] = None
    api_params: Optional[str] = None
    api_auth: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

    @validator('api_method')
    def validate_api_method(cls, v):
        """验证API方法"""
        if v is not None:
            allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
            if v.upper() not in allowed_methods:
                raise ValueError(f'API方法必须是以下之一: {", ".join(allowed_methods)}')
            return v.upper()
        return v

    @validator('api_headers', 'api_params', 'api_auth')
    def validate_json_fields(cls, v):
        """验证JSON格式字段"""
        if v is not None and v.strip():
            try:
                json.loads(v)
            except json.JSONDecodeError:
                raise ValueError('必须是有效的JSON格式')
        return v


class PlatformAPI(PlatformAPIBase):
    """电商平台API配置响应模式"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class PlatformAPIListResponse(BaseModel):
    """电商平台API配置列表响应模式"""
    items: List[PlatformAPI]
    total: int
    page: int
    page_size: int
    total_pages: int


class PlatformAPIQuery(BaseModel):
    """电商平台API配置查询参数"""
    platform_type: Optional[PlatformType] = None
    api_type: Optional[APIType] = None
    platform_name: Optional[str] = None
    api_name: Optional[str] = None
    is_active: Optional[bool] = None
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)


class APITypeInfo(BaseModel):
    """API类型信息"""
    value: str
    label: str
    description: str


class PlatformTypeInfo(BaseModel):
    """平台类型信息"""
    value: str
    label: str
    description: str


class PlatformAPIOptions(BaseModel):
    """电商平台API配置选项"""
    api_types: List[APITypeInfo]
    platform_types: List[PlatformTypeInfo]
    api_methods: List[str]
