"""
应用配置文件
包含数据库连接、应用设置等配置信息
"""

import os
from typing import Optional
from dotenv import load_dotenv
from pathlib import Path

# 加载环境变量 - 指定.env文件的完整路径
env_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=env_path)


class DatabaseConfig:
    """数据库配置"""

    # MySQL数据库配置 - 从环境变量读取
    DB_TYPE: str = os.getenv("DB_TYPE", "mysql")
    DB_HOST: str = os.getenv("DB_HOST", "***************")
    DB_PORT: int = int(os.getenv("DB_PORT", "3306"))
    DB_NAME: str = os.getenv("DB_NAME", "ecommerce_decision")
    DB_USER: str = os.getenv("DB_USER", "root")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "mysql_ZdArQf")
    DB_CHARSET: str = os.getenv("DB_CHARSET", "utf8mb4")

    # 连接池配置
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600

    # 是否显示SQL语句（开发环境使用）
    DB_ECHO: bool = False

    @classmethod
    def get_database_url(cls) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{cls.DB_USER}:{cls.DB_PASSWORD}@{cls.DB_HOST}:{cls.DB_PORT}/{cls.DB_NAME}?charset={cls.DB_CHARSET}"


class AppConfig:
    """应用配置"""

    # 应用基本信息
    APP_NAME: str = "电子商务决策系统"

    # API配置
    API_PREFIX: str = "/api"

    # 跨域配置
    CORS_ORIGINS: list = [
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ]

    # JWT配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30


class EnvironmentConfig:
    """环境配置"""

    # 环境类型：development, testing, production
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")

    # 调试模式
    DEBUG: bool = ENVIRONMENT == "development"


class Config(DatabaseConfig, AppConfig, EnvironmentConfig):
    """统一配置类"""
    pass


# 默认配置实例
config = Config()


class Settings:
    """FastAPI设置类，兼容原有代码"""
    # 应用配置
    app_name: str = config.APP_NAME
    debug: bool = config.DEBUG

    # 数据库配置
    database_url: str = config.get_database_url()

    # JWT配置
    secret_key: str = config.SECRET_KEY
    algorithm: str = config.ALGORITHM
    access_token_expire_minutes: int = config.ACCESS_TOKEN_EXPIRE_MINUTES

    # API配置
    api_v1_str: str = config.API_PREFIX


settings = Settings()
