"""
采购订单API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from datetime import datetime
import math
from app.core.database import get_db
from app.api.auth import get_current_user
from app.schemas.purchase import (
    PurchaseOrder, PurchaseOrderCreate, PurchaseOrderUpdate,
    PurchaseOrderListResponse, PurchaseOrderQuery, PurchaseOrderStats,
    PurchaseOrderStatus,
    PurchaseReceipt, PurchaseReceiptCreate, PurchaseReceiptUpdate,
    PurchaseReceiptListResponse, PurchaseReceiptQuery, PurchaseReceiptStatus,
    PurchaseReturn as PurchaseReturnSchema,
    PurchaseReturnCreate, PurchaseReturnUpdate, PurchaseReturnStatusUpdate,
    PurchaseReturnStats, PurchaseReturnQuery
)
from app.services.purchase_service import PurchaseService
from app.models.purchase import PurchaseReturn, PurchaseReturnItem, PurchaseReturnStatus, PurchaseOrder as PurchaseOrderModel, PurchaseReceipt, PurchaseReceiptItem
from app.models.supplier import Supplier
from app.models.warehouse import Warehouse
from app.models.inventory import Inventory


router = APIRouter()


@router.get("/", response_model=PurchaseOrderListResponse)
async def get_purchase_orders(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_no: Optional[str] = Query(None, description="订单号"),
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    status: Optional[PurchaseOrderStatus] = Query(None, description="订单状态"),
    created_by: Optional[str] = Query(None, description="创建人"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    has_received: Optional[bool] = Query(None, description="是否有收货数量"),
    db: Session = Depends(get_db)
):
    """获取采购订单列表"""
    
    service = PurchaseService(db)
    
    # 构建查询参数
    query = PurchaseOrderQuery(
        order_no=order_no,
        supplier_id=supplier_id,
        status=status,
        created_by=created_by,
        start_date=start_date,
        end_date=end_date,
        has_received=has_received
    )
    
    # 计算偏移量
    skip = (page - 1) * page_size
    
    # 获取数据
    orders, total = service.get_purchase_orders(skip=skip, limit=page_size, query=query)
    
    # 添加供应商名称
    for order in orders:
        if order.supplier:
            order.supplier_name = order.supplier.name
    
    # 计算总页数
    total_pages = math.ceil(total / page_size) if total > 0 else 1
    
    return PurchaseOrderListResponse(
        items=orders,
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages
    )


@router.get("/stats", response_model=PurchaseOrderStats)
async def get_purchase_order_stats(db: Session = Depends(get_db)):
    """获取采购订单统计信息"""
    service = PurchaseService(db)
    return service.get_purchase_order_stats()


@router.get("/available-for-receipt")
async def get_available_purchase_orders_for_receipt(db: Session = Depends(get_db)):
    """获取可用于创建入库单的采购订单（有剩余未入库数量的订单）"""
    service = PurchaseService(db)
    orders = service.get_available_orders_for_receipt()

    # 添加供应商名称
    for order in orders:
        if order.supplier:
            order.supplier_name = order.supplier.name

    return orders


@router.get("/{order_id}", response_model=PurchaseOrder)
async def get_purchase_order(order_id: int, db: Session = Depends(get_db)):
    """获取单个采购订单"""
    service = PurchaseService(db)
    order = service.get_purchase_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")
    
    # 添加供应商名称
    if order.supplier:
        order.supplier_name = order.supplier.name
    
    return order


@router.post("/", response_model=PurchaseOrder)
async def create_purchase_order(
    order_data: PurchaseOrderCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建采购订单"""
    service = PurchaseService(db)
    try:
        # 使用当前登录用户作为创建人
        order_data.created_by = current_user["username"]
        order = service.create_purchase_order(order_data)

        # 添加供应商名称
        if order.supplier:
            order.supplier_name = order.supplier.name

        return order
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")


@router.put("/{order_id}", response_model=PurchaseOrder)
async def update_purchase_order(
    order_id: int, 
    order_data: PurchaseOrderUpdate, 
    db: Session = Depends(get_db)
):
    """更新采购订单"""
    service = PurchaseService(db)
    try:
        order = service.update_purchase_order(order_id, order_data)
        if not order:
            raise HTTPException(status_code=404, detail="采购订单不存在")
        
        # 添加供应商名称
        if order.supplier:
            order.supplier_name = order.supplier.name
        
        return order
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{order_id}")
async def delete_purchase_order(order_id: int, db: Session = Depends(get_db)):
    """删除采购订单"""
    service = PurchaseService(db)
    try:
        success = service.delete_purchase_order(order_id)
        if not success:
            raise HTTPException(status_code=404, detail="采购订单不存在")
        return {"message": "采购订单删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{order_id}/submit")
async def submit_purchase_order(
    order_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """提交采购订单"""
    service = PurchaseService(db)

    # 检查订单是否存在且状态为草稿
    order = service.get_purchase_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    if order.status != PurchaseOrderStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的订单才能提交")

    # 更新为已提交状态，使用当前用户作为提交人
    update_data = PurchaseOrderUpdate(
        status=PurchaseOrderStatus.SUBMITTED,
        submitted_by=current_user["username"],
        submitted_at=datetime.now()
    )

    updated_order = service.update_purchase_order(order_id, update_data)
    return {"message": "采购订单提交成功", "order": updated_order}


@router.put("/{order_id}/approve")
async def approve_purchase_order(
    order_id: int,
    approved: bool = Query(True, description="是否通过审核"),
    remark: Optional[str] = Query(None, description="审核备注"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """审核采购订单"""
    service = PurchaseService(db)

    # 检查订单是否存在且状态为已提交
    order = service.get_purchase_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    if order.status != PurchaseOrderStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交状态的订单才能审核")

    # 根据审核结果更新状态
    if approved:
        new_status = PurchaseOrderStatus.APPROVED
        message = "采购订单审核通过"
    else:
        new_status = PurchaseOrderStatus.REJECTED
        message = "采购订单审核拒绝"

    update_data = PurchaseOrderUpdate(
        status=new_status,
        approved_by=current_user["username"],  # 使用当前用户作为审核人
        approved_at=datetime.now()
    )

    # 如果有审核备注，添加到备注字段
    if remark:
        update_data.remark = remark

    updated_order = service.update_purchase_order(order_id, update_data)
    return {"message": message, "order": updated_order}


@router.put("/{order_id}/recall")
async def recall_purchase_order(
    order_id: int,
    db: Session = Depends(get_db)
):
    """撤回采购订单"""
    service = PurchaseService(db)

    # 检查订单是否存在且状态为已提交
    order = service.get_purchase_order(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    if order.status != PurchaseOrderStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交状态的订单才能撤回")

    # 更新为草稿状态，清除提交信息
    update_data = PurchaseOrderUpdate(
        status=PurchaseOrderStatus.DRAFT,
        submitted_by=None,
        submitted_at=None
    )

    updated_order = service.update_purchase_order(order_id, update_data)
    return {"message": "采购订单撤回成功", "order": updated_order}


@router.put("/{order_id}/status")
async def update_order_status(
    order_id: int,
    status: PurchaseOrderStatus,
    approved_by: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """更新订单状态（保留兼容性）"""
    service = PurchaseService(db)

    update_data = PurchaseOrderUpdate(status=status)
    if status == PurchaseOrderStatus.APPROVED and approved_by:
        update_data.approved_by = approved_by
        update_data.approved_at = datetime.now()

    order = service.update_purchase_order(order_id, update_data)
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    return {"message": "订单状态更新成功"}


# ==================== 采购入库单相关API ====================

@router.get("/receipts/", response_model=PurchaseReceiptListResponse)
async def get_purchase_receipts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    receipt_no: Optional[str] = Query(None, description="入库单号"),
    purchase_order_no: Optional[str] = Query(None, description="采购订单号"),
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    warehouse_id: Optional[int] = Query(None, description="仓库ID"),
    status: Optional[PurchaseReceiptStatus] = Query(None, description="入库单状态"),
    created_by: Optional[str] = Query(None, description="创建人"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    only_returnable: Optional[bool] = Query(False, description="只显示有可退货商品的收货单"),
    db: Session = Depends(get_db)
):
    """获取采购入库单列表"""

    service = PurchaseService(db)

    # 构建查询参数
    query_params = PurchaseReceiptQuery(
        receipt_no=receipt_no,
        purchase_order_no=purchase_order_no,
        supplier_id=supplier_id,
        warehouse_id=warehouse_id,
        status=status,
        created_by=created_by,
        start_date=start_date,
        end_date=end_date,
        only_returnable=only_returnable
    )

    # 计算分页参数
    skip = (page - 1) * page_size

    # 获取数据
    receipts, total = service.get_purchase_receipts(skip=skip, limit=page_size, query=query_params)

    # 添加关联对象名称
    for receipt in receipts:
        if receipt.supplier:
            receipt.supplier_name = receipt.supplier.name
        if receipt.warehouse:
            receipt.warehouse_name = receipt.warehouse.name

    # 计算总页数
    total_pages = math.ceil(total / page_size) if total > 0 else 1

    return PurchaseReceiptListResponse(
        items=receipts,
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages
    )


@router.get("/receipts/stats")
async def get_purchase_receipt_stats(db: Session = Depends(get_db)):
    """获取采购入库单统计信息"""
    service = PurchaseService(db)
    return service.get_purchase_receipt_stats()


@router.get("/receipts/available-for-return")
def get_available_receipts(db: Session = Depends(get_db)):
    """获取可用的收货单列表（已入库状态且有可退货商品）"""
    from app.models.purchase import PurchaseReceipt, PurchaseReceiptItem
    from app.models.supplier import Supplier

    # 获取已入库状态且有可退货商品的收货单
    # 可退货数量 = 入库数量 - 已退货数量 > 0
    receipts = db.query(PurchaseReceipt).join(Supplier).join(PurchaseReceiptItem).filter(
        PurchaseReceipt.status == "received",
        PurchaseReceiptItem.quantity > PurchaseReceiptItem.returned_quantity
    ).distinct().order_by(PurchaseReceipt.receipt_date.desc()).all()

    result = []
    for receipt in receipts:
        # 计算可退货商品数量
        returnable_items = 0
        for item in receipt.items:
            if item.quantity > item.returned_quantity:
                returnable_items += 1

        result.append({
            "id": receipt.id,
            "receipt_no": receipt.receipt_no,
            "supplier_id": receipt.supplier_id,
            "supplier_name": receipt.supplier.name if receipt.supplier else "",
            "received_date": receipt.receipt_date.isoformat() if receipt.receipt_date else None,
            "total_amount": 0,  # 暂时设为0，后续可以从其他地方获取
            "status": receipt.status,
            "returnable_items_count": returnable_items  # 可退货商品数量
        })

    return result


@router.get("/receipts/{receipt_id}")
async def get_purchase_receipt(receipt_id: int, db: Session = Depends(get_db)):
    """获取单个采购入库单"""
    service = PurchaseService(db)
    receipt = service.get_purchase_receipt(receipt_id)

    if not receipt:
        raise HTTPException(status_code=404, detail="采购入库单不存在")

    # 添加关联对象名称
    if receipt.supplier:
        receipt.supplier_name = receipt.supplier.name
    if receipt.warehouse:
        receipt.warehouse_name = receipt.warehouse.name

    return receipt


@router.post("/receipts/")
async def create_purchase_receipt(
    receipt_data: PurchaseReceiptCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建采购入库单"""
    service = PurchaseService(db)
    try:
        # 使用当前用户作为创建人
        receipt_data.created_by = current_user["username"]
        receipt = service.create_purchase_receipt(receipt_data)

        # 添加关联对象名称
        if receipt.supplier:
            receipt.supplier_name = receipt.supplier.name
        if receipt.warehouse:
            receipt.warehouse_name = receipt.warehouse.name

        return receipt
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")


@router.put("/receipts/{receipt_id}")
async def update_purchase_receipt(
    receipt_id: int,
    receipt_data: PurchaseReceiptUpdate,
    db: Session = Depends(get_db)
):
    """更新采购入库单"""
    service = PurchaseService(db)
    try:
        receipt = service.update_purchase_receipt(receipt_id, receipt_data)
        if not receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        # 添加关联对象名称
        if receipt.supplier:
            receipt.supplier_name = receipt.supplier.name
        if receipt.warehouse:
            receipt.warehouse_name = receipt.warehouse.name

        return receipt
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/receipts/{receipt_id}")
async def delete_purchase_receipt(receipt_id: int, db: Session = Depends(get_db)):
    """删除采购入库单"""
    service = PurchaseService(db)
    try:
        success = service.delete_purchase_receipt(receipt_id)
        if not success:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "采购入库单删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/receipts/from-order/{order_id}")
async def create_receipt_from_order(
    order_id: int,
    warehouse_id: int = Query(..., description="仓库ID"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """从采购订单创建入库单"""
    service = PurchaseService(db)
    try:
        # 使用当前用户作为创建人
        receipt = service.create_receipt_from_order(order_id, warehouse_id, current_user["username"])

        # 添加关联对象名称
        if receipt.supplier:
            receipt.supplier_name = receipt.supplier.name
        if receipt.warehouse:
            receipt.warehouse_name = receipt.warehouse.name

        return receipt
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/submit")
async def submit_purchase_receipt(
    receipt_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """提交采购入库单"""
    service = PurchaseService(db)

    try:
        # 使用当前用户作为提交人
        updated_receipt = service.submit_purchase_receipt(receipt_id, current_user["username"])
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "采购入库单提交成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/approve")
async def approve_purchase_receipt(
    receipt_id: int,
    approved: bool = Query(True, description="是否通过审核"),
    remark: Optional[str] = Query(None, description="审核备注"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """审核采购入库单"""
    service = PurchaseService(db)

    try:
        # 使用当前用户作为审核人
        updated_receipt = service.approve_purchase_receipt(receipt_id, current_user["username"], approved, remark)
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        action = "通过" if approved else "拒绝"
        return {"message": f"采购入库单审核{action}成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/confirm")
async def confirm_purchase_receipt(
    receipt_id: int,
    confirmed_by: str = Query(..., description="确认人"),
    db: Session = Depends(get_db)
):
    """确定入库 - 将商品信息添加到库存表"""
    service = PurchaseService(db)

    try:
        updated_receipt = service.confirm_receipt(receipt_id, confirmed_by)
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "入库确认成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/recall")
async def recall_purchase_receipt(
    receipt_id: int,
    db: Session = Depends(get_db)
):
    """撤回采购入库单"""
    service = PurchaseService(db)

    try:
        updated_receipt = service.recall_purchase_receipt(receipt_id)
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "采购入库单撤回成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/receipts/{receipt_id}/cancel")
async def cancel_purchase_receipt(
    receipt_id: int,
    db: Session = Depends(get_db)
):
    """取消采购入库单"""
    service = PurchaseService(db)

    try:
        updated_receipt = service.cancel_purchase_receipt(receipt_id)
        if not updated_receipt:
            raise HTTPException(status_code=404, detail="采购入库单不存在")

        return {"message": "采购入库单取消成功", "receipt": updated_receipt}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# ==================== 采购退货单相关API ====================

def get_purchase_return_stats(db: Session) -> PurchaseReturnStats:
    """获取采购退货单统计信息"""
    from sqlalchemy import func

    # 基础统计
    total_returns = db.query(PurchaseReturn).count()
    draft_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.DRAFT).count()
    submitted_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.SUBMITTED).count()
    approved_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.APPROVED).count()
    returned_returns = db.query(PurchaseReturn).filter(PurchaseReturn.status == PurchaseReturnStatus.RETURNED).count()

    # 金额统计
    total_amount = db.query(func.sum(PurchaseReturn.total_amount)).scalar() or 0

    return PurchaseReturnStats(
        total_returns=total_returns,
        draft_returns=draft_returns,
        submitted_returns=submitted_returns,
        approved_returns=approved_returns,
        returned_returns=returned_returns,
        total_amount=total_amount
    )


@router.get("/returns/stats", response_model=PurchaseReturnStats)
def get_return_stats(db: Session = Depends(get_db)):
    """获取采购退货单统计信息"""
    return get_purchase_return_stats(db)


@router.get("/returns/", response_model=List[PurchaseReturnSchema])
def get_purchase_returns(
    return_no: Optional[str] = Query(None, description="退货单号"),
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    status: Optional[PurchaseReturnStatus] = Query(None, description="状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取采购退货单列表"""
    query = db.query(PurchaseReturn)

    # 应用筛选条件
    if return_no:
        query = query.filter(PurchaseReturn.return_no.contains(return_no))
    if supplier_id:
        query = query.filter(PurchaseReturn.supplier_id == supplier_id)
    if status:
        query = query.filter(PurchaseReturn.status == status)
    if start_date:
        query = query.filter(PurchaseReturn.return_date >= start_date)
    if end_date:
        query = query.filter(PurchaseReturn.return_date <= end_date)

    # 按修改时间倒序排序
    query = query.order_by(PurchaseReturn.updated_at.desc())

    # 分页
    offset = (page - 1) * page_size
    returns = query.offset(offset).limit(page_size).all()

    # 添加关联信息
    result = []
    for return_obj in returns:
        return_dict = return_obj.__dict__.copy()

        # 添加供应商名称
        if return_obj.supplier:
            return_dict['supplier_name'] = return_obj.supplier.name

        # 添加明细中的仓库名称
        items_with_warehouse = []
        for item in return_obj.items:
            item_dict = item.__dict__.copy()
            if item.warehouse:
                item_dict['warehouse_name'] = item.warehouse.name
            items_with_warehouse.append(item_dict)
        return_dict['items'] = items_with_warehouse

        result.append(PurchaseReturnSchema.model_validate(return_dict))

    return result


@router.get("/returns/warehouses-with-product/{product_id}")
def get_warehouses_with_product(product_id: int, db: Session = Depends(get_db)):
    """获取有指定产品库存的仓库列表"""
    from sqlalchemy import func

    try:
        # 查询有该产品库存的仓库，按仓库分组汇总库存
        warehouses_with_stock = db.query(
            Warehouse.id,
            Warehouse.name,
            func.sum(Inventory.available_stock).label('total_stock')
        ).join(
            Inventory, Warehouse.id == Inventory.warehouse_id
        ).filter(
            Inventory.product_id == product_id,
            Inventory.available_stock > 0,
            Warehouse.is_active == True,
            Warehouse.status == "active"
        ).group_by(
            Warehouse.id,
            Warehouse.name
        ).all()

        result = []
        for warehouse_id, warehouse_name, total_stock in warehouses_with_stock:
            result.append({
                "id": warehouse_id,
                "name": warehouse_name,
                "available_stock": int(total_stock),
                "display_name": f"{warehouse_name} (库存: {int(total_stock)})"
            })

        return result

    except Exception as e:
        print(f"获取仓库库存失败: {e}")
        return []


@router.get("/orders/{order_id}/returnable-quantities")
def get_returnable_quantities(order_id: int, db: Session = Depends(get_db)):
    """获取采购订单的可退货数量信息"""
    # 获取采购订单
    order = db.query(PurchaseOrderModel).filter(PurchaseOrderModel.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    # 获取该订单已有的退货数量
    existing_returns = db.query(PurchaseReturn).filter(
        PurchaseReturn.purchase_order_id == order_id,
        PurchaseReturn.status.in_([
            PurchaseReturnStatus.DRAFT,
            PurchaseReturnStatus.SUBMITTED,
            PurchaseReturnStatus.APPROVED,
            PurchaseReturnStatus.RETURNED,
            PurchaseReturnStatus.COMPLETED
        ])
    ).all()

    # 统计已退货数量
    returned_quantities = {}
    for return_obj in existing_returns:
        for item in return_obj.items:
            if item.product_id not in returned_quantities:
                returned_quantities[item.product_id] = 0
            returned_quantities[item.product_id] += item.return_quantity

    # 构建结果
    result = []
    for order_item in order.items:
        received_quantity = order_item.received_quantity or 0
        already_returned = returned_quantities.get(order_item.product_id, 0)
        returnable_quantity = received_quantity - already_returned

        result.append({
            "product_id": order_item.product_id,
            "product_name": order_item.product_name,
            "product_sku": order_item.product_sku,
            "ordered_quantity": order_item.quantity,
            "received_quantity": received_quantity,
            "returned_quantity": already_returned,
            "returnable_quantity": max(0, returnable_quantity),
            "unit_price": order_item.unit_price
        })

    return result


@router.get("/returns/{return_id}", response_model=PurchaseReturnSchema)
def get_purchase_return(return_id: int, db: Session = Depends(get_db)):
    """获取采购退货单详情"""
    return_obj = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not return_obj:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    return_dict = return_obj.__dict__.copy()

    # 添加关联信息
    if return_obj.supplier:
        return_dict['supplier_name'] = return_obj.supplier.name

    # 添加明细中的仓库名称
    items_with_warehouse = []
    for item in return_obj.items:
        item_dict = item.__dict__.copy()
        if item.warehouse:
            item_dict['warehouse_name'] = item.warehouse.name
        items_with_warehouse.append(item_dict)
    return_dict['items'] = items_with_warehouse

    return PurchaseReturnSchema.model_validate(return_dict)


@router.post("/returns/", response_model=PurchaseReturnSchema)
def create_purchase_return(
    return_data: PurchaseReturnCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建采购退货单"""
    # 验证供应商是否存在
    supplier = db.query(Supplier).filter(Supplier.id == return_data.supplier_id).first()
    if not supplier:
        raise HTTPException(status_code=404, detail="供应商不存在")

    # 生成退货单号（RT = Return）
    if not return_data.return_no:
        return_data.return_no = _generate_return_no(db)

    # 处理日期格式
    return_date = return_data.return_date
    if isinstance(return_date, str):
        from datetime import datetime
        try:
            return_date = datetime.fromisoformat(return_date.replace('Z', '+00:00'))
        except ValueError:
            # 如果是YYYY-MM-DD格式，转换为datetime
            return_date = datetime.strptime(return_date, '%Y-%m-%d')

    # 创建退货单
    db_return = PurchaseReturn(
        return_no=return_data.return_no,
        receipt_no=return_data.receipt_no,
        supplier_id=return_data.supplier_id,
        return_date=return_date,
        reason=return_data.reason,
        total_amount=return_data.total_amount,
        remark=return_data.remark,
        status=PurchaseReturnStatus.DRAFT,
        created_by=current_user["username"]  # 使用当前用户作为创建人
    )

    db.add(db_return)
    db.flush()  # 获取ID

    # 创建退货明细
    for item_data in return_data.items:
        # 验证必要字段
        if not item_data.warehouse_id or item_data.warehouse_id <= 0:
            raise HTTPException(status_code=400, detail=f"商品 {item_data.product_name} 必须选择退货仓库")

        if not item_data.product_id or item_data.product_id <= 0:
            raise HTTPException(status_code=400, detail="商品信息不完整")

        if item_data.return_quantity <= 0:
            raise HTTPException(status_code=400, detail=f"商品 {item_data.product_name} 的退货数量必须大于0")

        db_item = PurchaseReturnItem(
            purchase_return_id=db_return.id,
            product_id=item_data.product_id,
            product_name=item_data.product_name,
            product_sku=item_data.product_sku,
            batch_no=item_data.batch_no,
            warehouse_id=item_data.warehouse_id,
            receipt_line_number=item_data.receipt_line_number,
            return_quantity=item_data.return_quantity,
            unit_price=item_data.unit_price,
            total_price=item_data.total_price,
            quality_issue=item_data.quality_issue
        )
        db.add(db_item)

    db.commit()
    db.refresh(db_return)

    return_dict = db_return.__dict__.copy()
    if db_return.supplier:
        return_dict['supplier_name'] = db_return.supplier.name

    # 添加明细中的仓库名称
    items_with_warehouse = []
    for item in db_return.items:
        item_dict = item.__dict__.copy()
        if item.warehouse:
            item_dict['warehouse_name'] = item.warehouse.name
        items_with_warehouse.append(item_dict)
    return_dict['items'] = items_with_warehouse

    return PurchaseReturnSchema.model_validate(return_dict)


@router.put("/returns/{return_id}", response_model=PurchaseReturnSchema)
def update_purchase_return(return_id: int, return_data: PurchaseReturnUpdate, db: Session = Depends(get_db)):
    """更新采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    # 只有草稿状态才能修改
    if db_return.status != PurchaseReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能修改")

    # 更新基本信息
    update_data = return_data.model_dump(exclude_unset=True)
    items_data = update_data.pop('items', None)

    for field, value in update_data.items():
        setattr(db_return, field, value)

    # 更新明细
    if items_data is not None:
        # 注意：现在只保存收货单号，不再基于收货单ID进行验证

        # 删除原有明细
        db.query(PurchaseReturnItem).filter(PurchaseReturnItem.purchase_return_id == return_id).delete()

        # 创建新明细
        for item_data in items_data:
            db_item = PurchaseReturnItem(
                purchase_return_id=return_id,
                **item_data
            )
            db.add(db_item)

    db.commit()
    db.refresh(db_return)

    return_dict = db_return.__dict__.copy()
    if db_return.supplier:
        return_dict['supplier_name'] = db_return.supplier.name

    # 添加明细中的仓库名称
    items_with_warehouse = []
    for item in db_return.items:
        item_dict = item.__dict__.copy()
        if item.warehouse:
            item_dict['warehouse_name'] = item.warehouse.name
        items_with_warehouse.append(item_dict)
    return_dict['items'] = items_with_warehouse

    return PurchaseReturnSchema.model_validate(return_dict)


@router.delete("/returns/{return_id}")
def delete_purchase_return(return_id: int, db: Session = Depends(get_db)):
    """删除采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    # 只有草稿状态才能删除
    if db_return.status != PurchaseReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能删除")

    db.delete(db_return)
    db.commit()

    return {"message": "采购退货单删除成功"}


@router.post("/returns/{return_id}/submit")
def submit_purchase_return(
    return_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """提交采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    if db_return.status != PurchaseReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能提交")

    db_return.status = PurchaseReturnStatus.SUBMITTED
    db_return.submitted_at = datetime.now()
    db_return.submitted_by = current_user["username"]  # 使用当前用户作为提交人

    db.commit()

    return {"message": "采购退货单提交成功"}


@router.post("/returns/{return_id}/approve")
def approve_purchase_return(
    return_id: int,
    status_data: PurchaseReturnStatusUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """审核采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    if db_return.status != PurchaseReturnStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交的退货单才能审核")

    if status_data.status not in [PurchaseReturnStatus.APPROVED, PurchaseReturnStatus.REJECTED]:
        raise HTTPException(status_code=400, detail="审核状态只能是已审核或已拒绝")

    db_return.status = status_data.status
    db_return.approved_at = datetime.now()
    db_return.approved_by = current_user["username"]  # 使用当前用户作为审核人
    db_return.approval_note = status_data.note

    db.commit()

    action = "审核通过" if status_data.status == PurchaseReturnStatus.APPROVED else "审核拒绝"
    return {"message": f"采购退货单{action}"}


@router.post("/returns/{return_id}/return")
def return_purchase_return(
    return_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """确认退货"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="采购退货单不存在")

    if db_return.status != PurchaseReturnStatus.APPROVED:
        raise HTTPException(status_code=400, detail="只有已审核的退货单才能确认退货")

    db_return.status = PurchaseReturnStatus.RETURNED
    db_return.returned_at = datetime.now()
    db_return.returned_by = current_user["username"]  # 使用当前用户作为退货确认人

    # 更新库存（减少库存）- 采购退货意味着商品从仓库中退出，需要减少库存
    from app.services.inventory_service import InventoryService
    inventory_service = InventoryService(db)

    for item in db_return.items:
        try:
            # 减少库存
            inventory_service.reduce_stock(
                product_id=item.product_id,
                warehouse_id=item.warehouse_id,
                quantity=item.return_quantity,
                reason=f"采购退货确认: {db_return.return_no}",
                operator="system"
            )

            # 更新对应收货单明细的已退货数量
            if item.receipt_line_number and db_return.receipt_no:
                # 根据收货单号和行号找到对应的收货单明细
                receipt_item = db.query(PurchaseReceiptItem).join(PurchaseReceipt).filter(
                    PurchaseReceipt.receipt_no == db_return.receipt_no,
                    PurchaseReceiptItem.line_number == item.receipt_line_number,
                    PurchaseReceiptItem.product_id == item.product_id
                ).first()

                if receipt_item:
                    # 增加已退货数量
                    receipt_item.returned_quantity += item.return_quantity
                    print(f"更新收货单明细 {receipt_item.id} 的已退货数量: +{item.return_quantity} = {receipt_item.returned_quantity}")
                else:
                    print(f"警告: 未找到对应的收货单明细 (收货单号: {db_return.receipt_no}, 行号: {item.receipt_line_number}, 商品ID: {item.product_id})")

        except Exception as e:
            # 如果库存调整失败，回滚事务
            db.rollback()
            raise HTTPException(status_code=400, detail=f"商品 {item.product_name} 库存调整失败: {str(e)}")

    db.commit()

    return {"message": "采购退货确认成功"}





def _generate_return_no(db: Session) -> str:
    """生成退货单号"""
    from datetime import datetime
    from sqlalchemy import desc

    today = datetime.now().strftime("%Y%m%d")

    # 查找今天的最大退货单号
    latest_return = db.query(PurchaseReturn).filter(
        PurchaseReturn.return_no.like(f"RT{today}%")
    ).order_by(desc(PurchaseReturn.return_no)).first()

    if latest_return:
        # 提取序号并加1
        sequence = int(latest_return.return_no[-4:]) + 1
    else:
        sequence = 1

    return f"RT{today}{sequence:04d}"


# 退货单状态操作API
@router.put("/returns/{return_id}/submit")
async def submit_purchase_return(
    return_id: int,
    submitted_by: str = Query("系统用户", description="提交人"),
    db: Session = Depends(get_db)
):
    """提交采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="退货单不存在")

    if db_return.status != PurchaseReturnStatus.DRAFT:
        raise HTTPException(status_code=400, detail="只有草稿状态的退货单才能提交")

    db_return.status = PurchaseReturnStatus.SUBMITTED
    db_return.submitted_by = submitted_by
    db_return.submitted_at = datetime.now()

    db.commit()
    return {"message": "退货单提交成功"}


@router.put("/returns/{return_id}/withdraw")
async def withdraw_purchase_return(
    return_id: int,
    withdrawn_by: str = Query("系统用户", description="撤回人"),
    db: Session = Depends(get_db)
):
    """撤回采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="退货单不存在")

    if db_return.status != PurchaseReturnStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交状态的退货单才能撤回")

    db_return.status = PurchaseReturnStatus.DRAFT
    db_return.submitted_by = None
    db_return.submitted_at = None

    db.commit()
    return {"message": "退货单撤回成功"}


@router.put("/returns/{return_id}/approve")
async def approve_purchase_return(
    return_id: int,
    approval_data: dict,
    approved_by: str = Query("系统用户", description="审核人"),
    db: Session = Depends(get_db)
):
    """审核采购退货单"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="退货单不存在")

    if db_return.status != PurchaseReturnStatus.SUBMITTED:
        raise HTTPException(status_code=400, detail="只有已提交状态的退货单才能审核")

    status = approval_data.get('status')
    note = approval_data.get('note', '')

    if status == "approved" or status == PurchaseReturnStatus.APPROVED:
        db_return.status = PurchaseReturnStatus.APPROVED
        db_return.approved_by = approved_by
        db_return.approved_at = datetime.now()
        message = "退货单审核通过"
    elif status == "rejected" or status == PurchaseReturnStatus.REJECTED:
        db_return.status = PurchaseReturnStatus.REJECTED
        db_return.rejected_by = approved_by
        db_return.rejected_at = datetime.now()
        db_return.reject_reason = note
        message = "退货单审核拒绝"
    else:
        raise HTTPException(status_code=400, detail=f"无效的审核状态: {status}")

    db.commit()
    return {"message": message}


@router.put("/returns/{return_id}/return")
async def confirm_purchase_return(
    return_id: int,
    returned_by: str = Query("系统用户", description="退货确认人"),
    db: Session = Depends(get_db)
):
    """确认采购退货"""
    db_return = db.query(PurchaseReturn).filter(PurchaseReturn.id == return_id).first()
    if not db_return:
        raise HTTPException(status_code=404, detail="退货单不存在")

    if db_return.status != PurchaseReturnStatus.APPROVED:
        raise HTTPException(status_code=400, detail="只有已审核通过的退货单才能确认退货")

    db_return.status = PurchaseReturnStatus.RETURNED
    db_return.returned_by = returned_by
    db_return.returned_at = datetime.now()

    # 更新库存（减少库存）- 采购退货意味着商品从仓库中退出，需要减少库存
    from app.services.inventory_service import InventoryService
    inventory_service = InventoryService(db)

    for item in db_return.items:
        try:
            # 减少库存
            inventory_service.reduce_stock(
                product_id=item.product_id,
                warehouse_id=item.warehouse_id,
                quantity=item.return_quantity,
                reason=f"采购退货确认: {db_return.return_no}",
                operator=returned_by
            )

            # 更新对应收货单明细的已退货数量
            if item.receipt_line_number and db_return.receipt_no:
                # 根据收货单号和行号找到对应的收货单明细
                receipt_item = db.query(PurchaseReceiptItem).join(PurchaseReceipt).filter(
                    PurchaseReceipt.receipt_no == db_return.receipt_no,
                    PurchaseReceiptItem.line_number == item.receipt_line_number,
                    PurchaseReceiptItem.product_id == item.product_id
                ).first()

                if receipt_item:
                    # 增加已退货数量
                    receipt_item.returned_quantity += item.return_quantity
                    print(f"更新收货单明细 {receipt_item.id} 的已退货数量: +{item.return_quantity} = {receipt_item.returned_quantity}")
                else:
                    print(f"警告: 未找到对应的收货单明细 (收货单号: {db_return.receipt_no}, 行号: {item.receipt_line_number}, 商品ID: {item.product_id})")

        except Exception as e:
            # 如果库存调整失败，回滚事务
            db.rollback()
            raise HTTPException(status_code=400, detail=f"商品 {item.product_name} 库存调整失败: {str(e)}")

    db.commit()
    return {"message": "退货确认成功"}




@router.get("/orders/{order_id}/product-batches/{product_id}")
def get_product_batches_from_order(
    order_id: int,
    product_id: int,
    db: Session = Depends(get_db)
):
    """获取采购订单中指定商品当前在库的批次信息"""
    from app.models.inventory import Inventory
    from app.models.purchase import PurchaseReceipt, PurchaseReceiptItem

    # 获取采购订单
    order = db.query(PurchaseOrderModel).filter(PurchaseOrderModel.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="采购订单不存在")

    # 获取该订单的所有收货单中该商品的批次号
    order_batches = db.query(PurchaseReceiptItem.batch_no).join(PurchaseReceipt).filter(
        PurchaseReceipt.purchase_order_id == order_id,
        PurchaseReceipt.status == "completed",
        PurchaseReceiptItem.product_id == product_id,
        PurchaseReceiptItem.batch_no.isnot(None),
        PurchaseReceiptItem.batch_no != ""
    ).distinct().all()

    order_batch_nos = [batch[0] for batch in order_batches]

    if not order_batch_nos:
        return []

    # 查询这些批次在当前库存中的情况（库存>0）
    inventory_items = db.query(Inventory).filter(
        Inventory.product_id == product_id,
        Inventory.batch_no.in_(order_batch_nos),
        Inventory.current_stock > 0
    ).all()

    batches = []
    for item in inventory_items:
        batches.append({
            "batch_no": item.batch_no,
            "warehouse_id": item.warehouse_id,
            "warehouse_name": item.warehouse.name if item.warehouse else "",
            "current_stock": item.current_stock,  # 当前库存数量
            "returnable_quantity": item.current_stock,  # 可退货数量等于当前库存
            "unit_price": item.last_cost or 0.00,
            "last_updated": item.updated_at.isoformat() if item.updated_at else None
        })

    return batches


@router.get("/products/{product_id}/batches")
def get_product_all_batches(product_id: int, db: Session = Depends(get_db)):
    """获取指定商品当前在库的所有批次信息（库存>0）"""
    from app.models.inventory import Inventory

    # 查询该商品在库存中的所有批次（库存数量>0）
    inventory_items = db.query(Inventory).filter(
        Inventory.product_id == product_id,
        Inventory.current_stock > 0,
        Inventory.batch_no.isnot(None),  # 只获取有批次号的
        Inventory.batch_no != ""  # 批次号不为空
    ).all()

    batches = []
    for item in inventory_items:
        batches.append({
            "batch_no": item.batch_no,
            "warehouse_id": item.warehouse_id,
            "warehouse_name": item.warehouse.name if item.warehouse else "",
            "current_stock": item.current_stock,  # 当前库存数量
            "returnable_quantity": item.current_stock,  # 可退货数量等于当前库存
            "unit_price": item.last_cost or 0.00,
            "last_updated": item.updated_at.isoformat() if item.updated_at else None
        })

    return batches


@router.get("/receipts/{receipt_id}/product-batches/{product_id}")
def get_product_batches_from_receipt(
    receipt_id: int,
    product_id: int,
    db: Session = Depends(get_db)
):
    """获取收货单中指定商品的批次信息（基于收货数量）"""
    from app.models.purchase import PurchaseReceipt, PurchaseReceiptItem

    # 检查收货单是否存在
    receipt = db.query(PurchaseReceipt).filter(PurchaseReceipt.id == receipt_id).first()
    if not receipt:
        raise HTTPException(status_code=404, detail="收货单不存在")

    # 获取该收货单中该商品的所有批次
    receipt_items = db.query(PurchaseReceiptItem).filter(
        PurchaseReceiptItem.receipt_id == receipt_id,
        PurchaseReceiptItem.product_id == product_id,
        PurchaseReceiptItem.batch_no.isnot(None),
        PurchaseReceiptItem.batch_no != ""
    ).all()

    batches = []
    for item in receipt_items:
        batches.append({
            "batch_no": item.batch_no,
            "warehouse_id": receipt.warehouse_id,  # 使用收货单的仓库
            "warehouse_name": receipt.warehouse.name if receipt.warehouse else "",
            "received_quantity": item.quantity,  # 收货数量
            "returnable_quantity": item.quantity,  # 可退货数量等于收货数量
            "unit_price": 0.00,  # 暂时设为0，后续可以从产品信息获取
            "production_date": item.production_date.isoformat() if item.production_date else None,
            "expiry_date": item.expiry_date.isoformat() if item.expiry_date else None
        })

    return batches





@router.get("/receipts/{receipt_id}/items")
def get_receipt_items(receipt_id: int, db: Session = Depends(get_db)):
    """获取收货单明细"""
    from app.models.purchase import PurchaseReceipt, PurchaseReceiptItem

    # 检查收货单是否存在
    receipt = db.query(PurchaseReceipt).filter(PurchaseReceipt.id == receipt_id).first()
    if not receipt:
        raise HTTPException(status_code=404, detail="收货单不存在")

    # 获取收货单明细
    items = db.query(PurchaseReceiptItem).filter(
        PurchaseReceiptItem.receipt_id == receipt_id
    ).order_by(PurchaseReceiptItem.line_number).all()

    result = []
    for item in items:
        # 计算可退货数量
        returnable_quantity = item.quantity - item.returned_quantity

        result.append({
            "id": item.id,
            "line_number": item.line_number,
            "product_id": item.product_id,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "batch_no": item.batch_no,
            "warehouse_id": item.receipt.warehouse_id,  # 从收货单获取仓库ID
            "warehouse_name": item.receipt.warehouse.name if item.receipt.warehouse else "",
            "quantity": item.quantity,  # 入库数量
            "returned_quantity": item.returned_quantity,  # 已退货数量
            "returnable_quantity": returnable_quantity,  # 可退货数量
            "unit_price": 0.00,  # 暂时设为0，后续可以从产品信息获取
            "total_price": 0.00  # 暂时设为0
        })

    return result


@router.get("/batch-stock/{product_id}/{batch_no}/{warehouse_id}")
def get_batch_stock_in_warehouse(
    product_id: int,
    batch_no: str,
    warehouse_id: int,
    db: Session = Depends(get_db)
):
    """获取指定商品指定批次在指定仓库的库存信息"""
    from app.models.inventory import Inventory

    try:
        # 查询该商品该批次在该仓库的库存
        inventory = db.query(Inventory).filter(
            Inventory.product_id == product_id,
            Inventory.batch_no == batch_no,
            Inventory.warehouse_id == warehouse_id,
            Inventory.is_active == True
        ).first()

        if inventory:
            return {
                "product_id": product_id,
                "batch_no": batch_no,
                "warehouse_id": warehouse_id,
                "current_stock": inventory.current_stock or 0,
                "available_stock": inventory.available_stock or 0,
                "reserved_stock": inventory.reserved_stock or 0
            }
        else:
            return {
                "product_id": product_id,
                "batch_no": batch_no,
                "warehouse_id": warehouse_id,
                "current_stock": 0,
                "available_stock": 0,
                "reserved_stock": 0
            }
    except Exception as e:
        print(f"获取批次库存失败: {e}")
        return {
            "product_id": product_id,
            "batch_no": batch_no,
            "warehouse_id": warehouse_id,
            "current_stock": 0,
            "available_stock": 0,
            "reserved_stock": 0
        }
