import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// Element Plus 中文语言包
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 认证store
import { useAuthStore } from './stores/auth'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

const pinia = createPinia()
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

// 初始化认证状态
const authStore = useAuthStore()
authStore.initializeAuth()

// 设置定期检查token的定时器（每5分钟检查一次）
setInterval(() => {
  if (authStore.isLoggedIn) {
    authStore.checkAndRefreshToken()
  }
}, 5 * 60 * 1000)

app.mount('#app')
