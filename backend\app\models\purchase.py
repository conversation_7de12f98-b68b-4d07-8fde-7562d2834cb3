"""
采购相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Date, JSON, Boolean, Float, ForeignKey, Numeric, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class PurchaseOrder(Base):
    """采购订单模型"""
    __tablename__ = "purchase_orders"

    id = Column(Integer, primary_key=True, index=True)
    order_no = Column(String(50), unique=True, nullable=False, comment="采购订单号")
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=False, comment="供应商ID")
    
    # 订单信息
    total_amount = Column(Float, nullable=False, comment="订单总金额")
    status = Column(String(20), default="draft", comment="状态: draft, submitted, approved, rejected, purchasing, completed, cancelled")
    expected_date = Column(DateTime(timezone=True), comment="预期到货日期")
    
    # 创建信息
    created_by = Column(String(100), nullable=False, comment="创建人")
    submitted_by = Column(String(100), comment="提交人")
    submitted_at = Column(DateTime(timezone=True), comment="提交时间")
    approved_by = Column(String(100), comment="审批人")
    approved_at = Column(DateTime(timezone=True), comment="审批时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 备注
    remark = Column(Text, comment="备注")
    
    # 关联关系
    supplier = relationship("Supplier")
    items = relationship("PurchaseOrderItem", back_populates="order", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<PurchaseOrder(id={self.id}, order_no='{self.order_no}', status='{self.status}')>"


class PurchaseOrderItem(Base):
    """采购订单明细"""
    __tablename__ = "purchase_order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("purchase_orders.id"), nullable=False, comment="采购订单ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")

    # 行号
    line_number = Column(Integer, nullable=False, comment="行号")

    # 商品信息
    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_sku = Column(String(100), comment="商品SKU")
    
    # 数量和价格
    quantity = Column(Integer, nullable=False, comment="采购数量")
    unit_price = Column(Float, nullable=False, comment="单价")
    total_price = Column(Float, nullable=False, comment="小计")
    
    # 收货信息
    received_quantity = Column(Integer, default=0, comment="已收货数量")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关联关系
    order = relationship("PurchaseOrder", back_populates="items")
    product = relationship("Product")

    def __repr__(self):
        return f"<PurchaseOrderItem(id={self.id}, product_name='{self.product_name}', quantity={self.quantity})>"


class PurchaseReceipt(Base):
    """采购入库单模型"""
    __tablename__ = "purchase_receipts"

    id = Column(Integer, primary_key=True, index=True)
    receipt_no = Column(String(50), unique=True, nullable=False, comment="入库单号")
    purchase_order_id = Column(Integer, ForeignKey("purchase_orders.id"), comment="采购订单ID")
    purchase_order_no = Column(String(50), comment="采购订单号")
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=False, comment="供应商ID")
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False, comment="仓库ID")
    
    # 入库信息
    status = Column(String(20), default="draft", comment="状态: draft, submitted, approved, rejected, cancelled")
    receipt_date = Column(DateTime(timezone=True), comment="入库日期")

    # 创建信息
    created_by = Column(String(100), nullable=False, comment="创建人")

    # 提交信息
    submitted_by = Column(String(100), comment="提交人")
    submitted_at = Column(DateTime(timezone=True), comment="提交时间")

    # 审批信息
    approved_by = Column(String(100), comment="审批人")
    approved_at = Column(DateTime(timezone=True), comment="审批时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    
    # 备注
    remark = Column(Text, comment="备注")
    
    # 关联关系
    purchase_order = relationship("PurchaseOrder")
    supplier = relationship("Supplier")
    warehouse = relationship("Warehouse")
    items = relationship("PurchaseReceiptItem", back_populates="receipt", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<PurchaseReceipt(id={self.id}, receipt_no='{self.receipt_no}', status='{self.status}')>"


class PurchaseReceiptItem(Base):
    """采购入库单明细"""
    __tablename__ = "purchase_receipt_items"

    id = Column(Integer, primary_key=True, index=True)
    receipt_id = Column(Integer, ForeignKey("purchase_receipts.id"), nullable=False, comment="入库单ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    line_number = Column(Integer, nullable=False, comment="行号")
    
    # 商品信息
    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_sku = Column(String(100), comment="商品SKU")
    
    # 数量和批次
    quantity = Column(Integer, nullable=False, comment="数量")
    returned_quantity = Column(Integer, nullable=False, default=0, comment="已退货数量")
    batch_no = Column(String(100), comment="批次号")
    production_date = Column(Date, comment="生产日期")
    expiry_date = Column(Date, comment="到期日期")

    # 采购订单关联信息
    order_line_number = Column(Integer, comment="采购订单行号")

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关联关系
    receipt = relationship("PurchaseReceipt", back_populates="items")
    product = relationship("Product")

    def __repr__(self):
        return f"<PurchaseReceiptItem(id={self.id}, product_name='{self.product_name}', quantity={self.quantity})>"


class PurchaseReturnStatus(enum.Enum):
    """采购退货单状态"""
    DRAFT = "draft"           # 草稿
    SUBMITTED = "submitted"   # 已提交
    APPROVED = "approved"     # 已审核
    REJECTED = "rejected"     # 已拒绝
    RETURNED = "returned"     # 已退货（最终状态）
    CANCELLED = "cancelled"   # 已取消


class PurchaseReturn(Base):
    """采购退货单模型"""
    __tablename__ = "purchase_returns"

    id = Column(Integer, primary_key=True, index=True)
    return_no = Column(String(50), unique=True, index=True, nullable=False, comment="退货单号")

    # 关联信息
    receipt_no = Column(String(50), nullable=True, comment="收货单号")
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=False, comment="供应商ID")

    # 基本信息
    return_date = Column(DateTime, nullable=False, comment="退货日期")
    reason = Column(Text, nullable=False, comment="退货原因")
    total_amount = Column(Numeric(10, 2), nullable=False, default=0, comment="退货总金额")

    # 状态信息
    status = Column(Enum(PurchaseReturnStatus), default=PurchaseReturnStatus.DRAFT, comment="退货单状态")

    # 审核信息
    submitted_at = Column(DateTime, nullable=True, comment="提交时间")
    submitted_by = Column(String(50), nullable=True, comment="提交人")
    approved_at = Column(DateTime, nullable=True, comment="审核时间")
    approved_by = Column(String(50), nullable=True, comment="审核人")
    approval_note = Column(Text, nullable=True, comment="审核备注")

    # 退货信息
    returned_at = Column(DateTime, nullable=True, comment="退货时间")
    returned_by = Column(String(50), nullable=True, comment="退货人")

    # 备注信息
    remark = Column(Text, nullable=True, comment="备注")

    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    updated_by = Column(String(50), nullable=True, comment="更新人")

    # 关系
    supplier = relationship("Supplier", back_populates="purchase_returns")
    items = relationship("PurchaseReturnItem", back_populates="purchase_return", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<PurchaseReturn(id={self.id}, return_no='{self.return_no}', status='{self.status}')>"


class PurchaseReturnItem(Base):
    """采购退货单明细模型"""
    __tablename__ = "purchase_return_items"

    id = Column(Integer, primary_key=True, index=True)
    purchase_return_id = Column(Integer, ForeignKey("purchase_returns.id"), nullable=False, comment="采购退货单ID")

    # 商品信息
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_sku = Column(String(100), nullable=True, comment="商品SKU")
    batch_no = Column(String(100), nullable=True, comment="批次号")
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False, comment="退货仓库ID")

    # 收货单关联信息
    receipt_line_number = Column(Integer, nullable=True, comment="收货单行号")

    # 退货信息
    return_quantity = Column(Integer, nullable=False, comment="退货数量")
    unit_price = Column(Numeric(10, 2), nullable=False, comment="单价")
    total_price = Column(Numeric(10, 2), nullable=False, comment="小计金额")

    # 质量信息
    quality_issue = Column(String(200), nullable=True, comment="质量问题描述")

    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    purchase_return = relationship("PurchaseReturn", back_populates="items")
    product = relationship("Product")
    warehouse = relationship("Warehouse")

    def __repr__(self):
        return f"<PurchaseReturnItem(id={self.id}, product_name='{self.product_name}', return_quantity={self.return_quantity})>"
