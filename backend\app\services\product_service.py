from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
import json
import random
from app.models.product import Product
from app.schemas.product import (
    ProductCreate, ProductUpdate, ProductQuery
)

class ProductService:
    def __init__(self, db: Session):
        self.db = db
    
    def get_products(self, skip: int = 0, limit: int = 100, category: str = None) -> List[Product]:
        """获取商品列表"""
        query = self.db.query(Product)
        if category:
            query = query.filter(Product.category == category)
        return query.offset(skip).limit(limit).all()

    def get_products_paginated(self, query_params: ProductQuery) -> Tuple[List[Product], int]:
        """分页获取商品列表"""
        query = self.db.query(Product)

        # 应用过滤条件
        if query_params.name:
            query = query.filter(Product.name.contains(query_params.name))

        if query_params.category:
            query = query.filter(Product.category == query_params.category)

        if query_params.brand:
            query = query.filter(Product.brand.contains(query_params.brand))

        if query_params.status:
            query = query.filter(Product.status == query_params.status)

        if query_params.min_price is not None:
            query = query.filter(Product.price >= query_params.min_price)

        if query_params.max_price is not None:
            query = query.filter(Product.price <= query_params.max_price)

        if query_params.low_stock:
            query = query.filter(Product.stock < 10)  # 低库存阈值为10

        # 获取总数
        total = query.count()

        # 分页和排序
        products = query.order_by(Product.created_at.desc()).offset(
            (query_params.page - 1) * query_params.page_size
        ).limit(query_params.page_size).all()

        return products, total
    
    def get_product(self, product_id: int) -> Optional[Product]:
        """获取单个商品"""
        return self.db.query(Product).filter(Product.id == product_id).first()
    
    def create_product(self, product_data: ProductCreate) -> Product:
        """创建新商品"""
        # 计算利润率
        profit_margin = ((product_data.price - product_data.cost) / product_data.price) * 100
        
        db_product = Product(
            **product_data.dict(),
            profit_margin=profit_margin
        )
        self.db.add(db_product)
        self.db.commit()
        self.db.refresh(db_product)
        return db_product
    
    def update_product(self, product_id: int, product_data: ProductUpdate) -> Optional[Product]:
        """更新商品"""
        db_product = self.get_product(product_id)
        if not db_product:
            return None
        
        update_data = product_data.dict(exclude_unset=True)
        
        # 重新计算利润率
        if 'price' in update_data or 'cost' in update_data:
            price = update_data.get('price', db_product.price)
            cost = update_data.get('cost', db_product.cost)
            update_data['profit_margin'] = ((price - cost) / price) * 100
        
        for field, value in update_data.items():
            setattr(db_product, field, value)
        
        self.db.commit()
        self.db.refresh(db_product)
        return db_product
    
    def delete_product(self, product_id: int) -> bool:
        """删除商品"""
        db_product = self.get_product(product_id)
        if not db_product:
            return False
        
        self.db.delete(db_product)
        self.db.commit()
        return True

            recommendation_reasons[product.id] = "、".join(reasons) or "综合评分较高"

        return ProductRecommendationResponse(
            products=products,
            recommendation_reasons=recommendation_reasons
        )

    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        total_products = self.db.query(Product).count()
        categories = self.db.query(Product.category).distinct().all()

        # 计算平均利润率
        products = self.db.query(Product).all()
        avg_profit_margin = sum(p.profit_margin or 0 for p in products) / len(products) if products else 0

        # 热门商品
        hot_products = self.db.query(Product).order_by(Product.recommendation_score.desc()).limit(5).all()

        return {
            "total_products": total_products,
            "total_categories": len(categories),
            "average_profit_margin": round(avg_profit_margin, 2),
            "hot_products": [{"id": p.id, "name": p.name, "score": p.recommendation_score} for p in hot_products],
            "category_distribution": self._get_category_distribution(),
            "recent_analysis": self._get_recent_analysis()
        }

    # 私有辅助方法
    def _calculate_initial_score(self, product_data: ProductCreate) -> float:
        """计算初始推荐评分"""
        profit_margin = ((product_data.price - product_data.cost) / product_data.price) * 100

        score = 0.5  # 基础分
        if profit_margin > 30:
            score += 0.3
        elif profit_margin > 20:
            score += 0.2
        elif profit_margin > 10:
            score += 0.1

        if product_data.stock > 100:
            score += 0.1

        return min(score, 1.0)

    def _analyze_market_trend(self, product: Product) -> Dict[str, Any]:
        """分析市场趋势"""
        # 模拟趋势分析
        trend_score = random.uniform(0.3, 0.9)

        return {
            "result": f"该商品市场趋势{'良好' if trend_score > 0.6 else '一般'}，趋势评分: {trend_score:.2f}",
            "confidence": trend_score,
            "data": {
                "trend_score": trend_score,
                "market_demand": random.choice(["高", "中", "低"]),
                "seasonal_factor": random.uniform(0.8, 1.2),
                "growth_potential": random.uniform(-0.1, 0.3)
            }
        }

    def _analyze_competition(self, product: Product) -> Dict[str, Any]:
        """分析竞争情况"""
        competition_level = random.uniform(0.2, 0.8)

        return {
            "result": f"竞争激烈程度: {'高' if competition_level > 0.6 else '中等' if competition_level > 0.4 else '低'}",
            "confidence": 0.8,
            "data": {
                "competition_level": competition_level,
                "competitor_count": random.randint(3, 15),
                "price_advantage": random.choice(["有优势", "持平", "劣势"]),
                "market_share": random.uniform(0.05, 0.25)
            }
        }

    def _analyze_sales_performance(self, product: Product) -> Dict[str, Any]:
        """分析销售表现"""
        performance_score = random.uniform(0.4, 0.9)

        return {
            "result": f"销售表现{'优秀' if performance_score > 0.7 else '良好' if performance_score > 0.5 else '一般'}",
            "confidence": 0.85,
            "data": {
                "performance_score": performance_score,
                "monthly_sales": random.randint(50, 500),
                "conversion_rate": random.uniform(0.02, 0.08),
                "customer_rating": random.uniform(3.5, 4.8)
            }
        }

    def _analyze_recommendation_potential(self, product: Product) -> Dict[str, Any]:
        """分析推荐潜力"""
        potential_score = (product.recommendation_score or 0.5)

        return {
            "result": f"推荐潜力: {'高' if potential_score > 0.7 else '中等' if potential_score > 0.5 else '低'}",
            "confidence": 0.9,
            "data": {
                "potential_score": potential_score,
                "profit_margin": product.profit_margin,
                "stock_level": product.stock,
                "category_popularity": random.uniform(0.3, 0.9)
            }
        }

    def _get_top_categories(self) -> List[Dict[str, Any]]:
        """获取热门分类"""
        categories = self.db.query(Product.category).distinct().all()
        return [
            {"name": cat[0], "product_count": random.randint(10, 100)}
            for cat in categories[:5]
        ]

    def _generate_trend_recommendations(self, products: List[Product]) -> List[str]:
        """生成趋势建议"""
        recommendations = [
            "关注高利润率商品的库存管理",
            "考虑扩大热门分类的商品线",
            "优化定价策略以提高竞争力",
            "加强市场趋势监控和预测"
        ]
        return random.sample(recommendations, 3)

    def _get_category_distribution(self) -> List[Dict[str, Any]]:
        """获取分类分布"""
        categories = self.db.query(Product.category).distinct().all()
        distribution = []
        for cat in categories:
            count = self.db.query(Product).filter(Product.category == cat[0]).count()
            distribution.append({"category": cat[0], "count": count})
        return distribution

    def _get_recent_analysis(self) -> List[Dict[str, Any]]:
        """获取最近的分析记录"""
        recent = self.db.query(ProductAnalysis).order_by(ProductAnalysis.created_at.desc()).limit(5).all()
        return [
            {
                "id": analysis.id,
                "product_id": analysis.product_id,
                "type": analysis.analysis_type,
                "confidence": analysis.confidence_score,
                "created_at": analysis.created_at.isoformat()
            }
            for analysis in recent
        ]