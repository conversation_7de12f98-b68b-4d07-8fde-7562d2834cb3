<template>
  <div class="platform-api-container">
    <div class="page-header">
      <h1>电商平台API管理</h1>
      <p>管理各个电商平台的API接口配置，包括库存查询、订单管理等接口设置</p>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="平台类型">
            <el-select v-model="searchForm.platform_type" placeholder="选择平台类型" clearable style="width: 150px">
              <el-option
                v-for="platform in options.platform_types"
                :key="platform.value"
                :label="platform.label"
                :value="platform.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="API类型">
            <el-select v-model="searchForm.api_type" placeholder="选择API类型" clearable style="width: 150px">
              <el-option
                v-for="apiType in options.api_types"
                :key="apiType.value"
                :label="apiType.label"
                :value="apiType.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="平台名称">
            <el-input v-model="searchForm.platform_name" placeholder="输入平台名称" clearable style="width: 150px" />
          </el-form-item>
          <el-form-item label="API名称">
            <el-input v-model="searchForm.api_name" placeholder="输入API名称" clearable style="width: 150px" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.is_active" placeholder="选择状态" clearable style="width: 100px">
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增API配置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card>
        <el-table :data="tableData" v-loading="loading" stripe>
          <el-table-column prop="platform_name" label="平台名称" width="120" />
          <el-table-column prop="platform_type" label="平台类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getPlatformTypeTagType(row.platform_type)">
                {{ getPlatformTypeLabel(row.platform_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="api_name" label="API名称" width="150" />
          <el-table-column prop="api_type" label="API类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getAPITypeTagType(row.api_type)">
                {{ getAPITypeLabel(row.api_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="api_url" label="API地址" min-width="200" show-overflow-tooltip />
          <el-table-column prop="api_method" label="请求方法" width="80">
            <template #default="{ row }">
              <el-tag :type="getMethodTagType(row.api_method)" size="small">
                {{ row.api_method }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_active" label="状态" width="80">
            <template #default="{ row }">
              <el-switch
                v-model="row.is_active"
                @change="handleToggleStatus(row)"
                :loading="row.statusLoading"
              />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.page_size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="平台类型" prop="platform_type">
              <el-select v-model="formData.platform_type" placeholder="选择平台类型" style="width: 100%">
                <el-option
                  v-for="platform in options.platform_types"
                  :key="platform.value"
                  :label="platform.label"
                  :value="platform.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平台名称" prop="platform_name">
              <el-input v-model="formData.platform_name" placeholder="输入平台名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="API类型" prop="api_type">
              <el-select v-model="formData.api_type" placeholder="选择API类型" style="width: 100%">
                <el-option
                  v-for="apiType in options.api_types"
                  :key="apiType.value"
                  :label="apiType.label"
                  :value="apiType.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="API名称" prop="api_name">
              <el-input v-model="formData.api_name" placeholder="输入API名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="API地址" prop="api_url">
              <el-input v-model="formData.api_url" placeholder="输入API地址" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="请求方法" prop="api_method">
              <el-select v-model="formData.api_method" placeholder="选择请求方法" style="width: 100%">
                <el-option
                  v-for="method in options.api_methods"
                  :key="method"
                  :label="method"
                  :value="method"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="请求头配置">
          <el-input
            v-model="formData.api_headers"
            type="textarea"
            :rows="3"
            placeholder="JSON格式，例如：{&quot;Content-Type&quot;: &quot;application/json&quot;}"
          />
        </el-form-item>
        <el-form-item label="请求参数配置">
          <el-input
            v-model="formData.api_params"
            type="textarea"
            :rows="3"
            placeholder="JSON格式，例如：{&quot;page&quot;: 1, &quot;size&quot;: 20}"
          />
        </el-form-item>
        <el-form-item label="认证配置">
          <el-input
            v-model="formData.api_auth"
            type="textarea"
            :rows="3"
            placeholder="JSON格式，例如：{&quot;type&quot;: &quot;bearer&quot;, &quot;token&quot;: &quot;your_token&quot;}"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="2"
            placeholder="输入API描述"
          />
        </el-form-item>
        <el-form-item label="启用状态">
          <el-switch v-model="formData.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { Search, Refresh, Plus, Edit, Delete } from '@element-plus/icons-vue'
import {
  platformApiApi,
  type PlatformAPI,
  type PlatformAPICreate,
  type PlatformAPIUpdate,
  type PlatformAPIQuery,
  type PlatformAPIOptions,
  PlatformType,
  APIType
} from '@/api/platform-api'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const tableData = ref<PlatformAPI[]>([])
const options = ref<PlatformAPIOptions>({
  api_types: [],
  platform_types: [],
  api_methods: []
})

// 搜索表单
const searchForm = reactive<PlatformAPIQuery>({
  platform_type: undefined,
  api_type: undefined,
  platform_name: '',
  api_name: '',
  is_active: undefined,
  page: 1,
  page_size: 20
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0,
  total_pages: 0
})

// 表单数据
const formData = reactive<PlatformAPICreate>({
  platform_type: PlatformType.TAOBAO,
  platform_name: '',
  api_type: APIType.INVENTORY_QUERY,
  api_name: '',
  api_url: '',
  api_method: 'GET',
  api_headers: '',
  api_params: '',
  api_auth: '',
  description: '',
  is_active: true
})

// 编辑状态
const editingId = ref<number | null>(null)

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑API配置' : '新增API配置'
})

// 表单验证规则
const formRules = {
  platform_type: [
    { required: true, message: '请选择平台类型', trigger: 'change' }
  ],
  platform_name: [
    { required: true, message: '请输入平台名称', trigger: 'blur' },
    { min: 1, max: 100, message: '平台名称长度在1到100个字符', trigger: 'blur' }
  ],
  api_type: [
    { required: true, message: '请选择API类型', trigger: 'change' }
  ],
  api_name: [
    { required: true, message: '请输入API名称', trigger: 'blur' },
    { min: 1, max: 100, message: 'API名称长度在1到100个字符', trigger: 'blur' }
  ],
  api_url: [
    { required: true, message: '请输入API地址', trigger: 'blur' },
    { min: 1, max: 500, message: 'API地址长度在1到500个字符', trigger: 'blur' }
  ],
  api_method: [
    { required: true, message: '请选择请求方法', trigger: 'change' }
  ]
}

// 方法
const loadOptions = async () => {
  try {
    options.value = await platformApiApi.getOptions()
  } catch (error) {
    console.error('加载选项失败:', error)
    ElMessage.error('加载选项失败')
  }
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      page_size: pagination.page_size
    }
    const response = await platformApiApi.getPlatformAPIs(params)
    tableData.value = response.items.map(item => ({
      ...item,
      statusLoading: false
    }))
    pagination.total = response.total
    pagination.total_pages = response.total_pages
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    platform_type: undefined,
    api_type: undefined,
    platform_name: '',
    api_name: '',
    is_active: undefined
  })
  handleSearch()
}

const handleAdd = () => {
  editingId.value = null
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: PlatformAPI) => {
  editingId.value = row.id
  Object.assign(formData, {
    platform_type: row.platform_type,
    platform_name: row.platform_name,
    api_type: row.api_type,
    api_name: row.api_name,
    api_url: row.api_url,
    api_method: row.api_method,
    api_headers: row.api_headers || '',
    api_params: row.api_params || '',
    api_auth: row.api_auth || '',
    description: row.description || '',
    is_active: row.is_active
  })
  dialogVisible.value = true
}

const handleDelete = async (row: PlatformAPI) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除API配置"${row.api_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await platformApiApi.deletePlatformAPI(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleToggleStatus = async (row: PlatformAPI & { statusLoading?: boolean }) => {
  row.statusLoading = true
  try {
    await platformApiApi.togglePlatformAPIStatus(row.id)
    ElMessage.success(`API配置已${row.is_active ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('状态切换失败:', error)
    ElMessage.error('状态切换失败')
    // 恢复原状态
    row.is_active = !row.is_active
  } finally {
    row.statusLoading = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证JSON格式
    const jsonFields = ['api_headers', 'api_params', 'api_auth']
    for (const field of jsonFields) {
      const value = formData[field as keyof typeof formData] as string
      if (value && value.trim()) {
        try {
          JSON.parse(value)
        } catch {
          ElMessage.error(`${field === 'api_headers' ? '请求头' : field === 'api_params' ? '请求参数' : '认证'}配置必须是有效的JSON格式`)
          return
        }
      }
    }

    submitLoading.value = true

    if (editingId.value) {
      // 更新
      const updateData: PlatformAPIUpdate = { ...formData }
      await platformApiApi.updatePlatformAPI(editingId.value, updateData)
      ElMessage.success('更新成功')
    } else {
      // 创建
      await platformApiApi.createPlatformAPI(formData)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadData()
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error(error.response?.data?.detail || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, {
    platform_type: PlatformType.TAOBAO,
    platform_name: '',
    api_type: APIType.INVENTORY_QUERY,
    api_name: '',
    api_url: '',
    api_method: 'GET',
    api_headers: '',
    api_params: '',
    api_auth: '',
    description: '',
    is_active: true
  })
  formRef.value?.clearValidate()
}

const handleSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 辅助方法
const getPlatformTypeLabel = (type: string) => {
  const platform = options.value.platform_types.find(p => p.value === type)
  return platform?.label || type
}

const getAPITypeLabel = (type: string) => {
  const apiType = options.value.api_types.find(a => a.value === type)
  return apiType?.label || type
}

const getPlatformTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    taobao: 'warning',
    tmall: 'danger',
    jd: 'success',
    pdd: 'info',
    douyin: 'primary',
    kuaishou: 'warning',
    wechat: 'success',
    custom: 'info'
  }
  return typeMap[type] || 'info'
}

const getAPITypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    inventory_query: 'primary',
    inventory_update: 'warning',
    order_list: 'success',
    order_detail: 'info',
    product_list: 'primary',
    product_detail: 'info'
  }
  return typeMap[type] || 'info'
}

const getMethodTagType = (method: string) => {
  const methodMap: Record<string, string> = {
    GET: 'success',
    POST: 'primary',
    PUT: 'warning',
    DELETE: 'danger',
    PATCH: 'info'
  }
  return methodMap[method] || 'info'
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadOptions()
  loadData()
})
</script>

<style scoped>
.platform-api-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.search-section {
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-dialog__body) {
  padding: 20px 20px 0 20px;
}

:deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
}
</style>
