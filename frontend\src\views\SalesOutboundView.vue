<template>
  <div class="sales-outbound-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>销售出库单</h2>
        <p class="page-description">管理销售出库单，跟踪商品出库情况</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建出库单
        </el-button>
        <el-button @click="exportOutbounds">
          <el-icon><Download /></el-icon>
          导出出库单
        </el-button>
      </div>
    </div>

    <!-- 出库单概览 -->
    <el-row :gutter="20" class="outbound-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Tickets /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ outboundStats.total_outbounds }}</div>
              <div class="overview-label">出库单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon pending">
              <el-icon size="32"><Clock /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ outboundStats.pending_outbounds }}</div>
              <div class="overview-label">草稿</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon processing">
              <el-icon size="32"><Loading /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ outboundStats.approved_outbounds }}</div>
              <div class="overview-label">已审核</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon completed">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ outboundStats.completed_outbounds }}</div>
              <div class="overview-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="单号搜索">
          <el-input
            v-model="searchForm.order_no"
            placeholder="请输入出库单号或销售订单号"
            clearable
            style="width: 250px"
          />
        </el-form-item>

        <el-form-item label="客户">
          <el-select v-model="searchForm.customer_id" placeholder="选择客户" clearable style="width: 200px">
            <el-option
              v-for="customer in customers"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="单据状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="草稿" value="pending" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="approved" />
            <el-option label="已完成" value="completed" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <el-form-item label="出库日期">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="fetchSalesOutbounds" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button @click="fetchSalesOutbounds">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-button-group>
            <el-button 
              :type="viewMode === 'table' ? 'primary' : ''"
              @click="viewMode = 'table'"
            >
              <el-icon><List /></el-icon>
              表格视图
            </el-button>
            <el-button 
              :type="viewMode === 'card' ? 'primary' : ''"
              @click="viewMode = 'card'"
            >
              <el-icon><Grid /></el-icon>
              卡片视图
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="salesOutbounds" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55" />

          <el-table-column prop="outbound_no" label="出库单号" width="150">
            <template #default="{ row }">
              <el-link type="primary" @click="viewOutboundDetail(row)">
                {{ row.outbound_no }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column prop="sales_order_no" label="销售订单号" width="150">
            <template #default="{ row }">
              <el-link
                v-if="row.sales_order_no"
                type="success"
                @click="viewSalesOrder(row.sales_order_no)"
              >
                {{ row.sales_order_no }}
              </el-link>
              <span v-else class="text-muted">手动创建</span>
            </template>
          </el-table-column>

          <el-table-column prop="customer_name" label="客户名称" width="200">
            <template #default="{ row }">
              <span v-if="row.customer_name">{{ row.customer_name }}</span>
              <span v-else class="text-muted">{{ getCustomerName(row.customer_id) }}</span>
            </template>
          </el-table-column>



          <el-table-column prop="total_quantity" label="商品种类" width="100">
            <template #default="{ row }">
              <span>{{ row.items?.length || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="quantity" label="出库数量" width="120">
            <template #default="{ row }">
              <span>{{ row.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="单据状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="outbound_date" label="出库日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.outbound_date) }}
            </template>
          </el-table-column>

          <el-table-column prop="created_by" label="创建人" width="100" />

          <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewOutboundDetail(row)">查看</el-button>

                <!-- 待处理状态：提交按钮 -->
                <el-button
                  v-if="row.status === 'pending'"
                  size="small"
                  type="success"
                  @click="handleOutboundAction('submit', row)"
                >
                  提交
                </el-button>

                <!-- 已提交状态：撤销按钮 -->
                <el-button
                  v-if="row.status === 'submitted'"
                  size="small"
                  type="warning"
                  @click="handleOutboundAction('revoke', row)"
                >
                  撤销
                </el-button>

                <el-dropdown
                  @command="(command: string) => handleOutboundAction(command, row)"
                  v-if="row.status !== 'completed' && row.status !== 'cancelled' && row.status !== 'rejected'"
                >
                  <el-button size="small">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <!-- 待处理状态的操作 -->
                      <el-dropdown-item command="edit" v-if="row.status === 'pending'">编辑</el-dropdown-item>
                      <el-dropdown-item divided command="delete" v-if="row.status === 'pending'">删除</el-dropdown-item>

                      <!-- 已提交状态的操作 -->
                      <el-dropdown-item command="approve" v-if="row.status === 'submitted'">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="row.status === 'submitted'">审核拒绝</el-dropdown-item>

                      <!-- 已审核通过状态的操作 -->
                      <el-dropdown-item command="complete" v-if="row.status === 'approved'">确认完成</el-dropdown-item>
                      <el-dropdown-item command="cancel_delivery" v-if="row.status === 'approved'">取消发货</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="outbounds-grid">
          <div
            v-for="outbound in salesOutbounds"
            :key="outbound.id"
            class="outbound-card"
          >
            <div class="card-header">
              <div class="outbound-info">
                <h4 class="outbound-no">{{ outbound.outbound_no }}</h4>
                <p class="customer-name">{{ outbound.customer_name }}</p>
                <p class="warehouse-name">仓库: {{ getWarehouseNames(outbound.items || []) }}</p>
              </div>
              <div class="outbound-status">
                <el-tag :type="getStatusTagType(outbound.status)" size="small">
                  {{ getStatusLabel(outbound.status) }}
                </el-tag>
              </div>
            </div>

            <div class="card-content">
              <div class="outbound-details">
                <div class="detail-row" v-if="outbound.sales_order_no">
                  <span class="label">销售订单:</span>
                  <el-link type="success" size="small" @click="viewSalesOrder(outbound.sales_order_no)">
                    {{ outbound.sales_order_no }}
                  </el-link>
                </div>
                <div class="detail-row">
                  <span class="label">商品种类:</span>
                  <span class="value">{{ outbound.items?.length || 0 }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">出库数量:</span>
                  <span class="value">{{ outbound.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0 }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">出库日期:</span>
                  <span class="value">{{ formatDate(outbound.outbound_date) }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">创建人:</span>
                  <span class="value">{{ outbound.created_by }}</span>
                </div>
              </div>
            </div>

            <div class="card-actions">
              <el-button size="small" @click="viewOutboundDetail(outbound)">查看详情</el-button>
              <el-button
                v-if="outbound.status === 'pending'"
                size="small"
                type="success"
                @click="handleOutboundAction('submit', outbound)"
              >
                提交
              </el-button>
              <el-button
                v-if="outbound.status === 'submitted'"
                size="small"
                type="primary"
                @click="handleOutboundAction('approve', outbound)"
              >
                审核通过
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchSalesOutbounds"
        @current-change="fetchSalesOutbounds"
      />
    </div>

    <!-- 详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="出库单详情" width="1000px">
      <div v-if="selectedOutbound" class="outbound-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="出库单号">{{ selectedOutbound.outbound_no }}</el-descriptions-item>
          <el-descriptions-item label="销售订单号">
            <el-link
              v-if="selectedOutbound.sales_order_no"
              type="success"
              @click="viewSalesOrder(selectedOutbound.sales_order_no)"
            >
              {{ selectedOutbound.sales_order_no }}
            </el-link>
            <span v-else class="text-muted">手动创建</span>
          </el-descriptions-item>
          <el-descriptions-item label="客户">{{ selectedOutbound.customer_name }}</el-descriptions-item>
          <el-descriptions-item label="仓库">{{ getWarehouseNames(selectedOutbound.items || []) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedOutbound.status)">
              {{ getStatusLabel(selectedOutbound.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="出库日期">{{ formatDate(selectedOutbound.outbound_date) }}</el-descriptions-item>
          <el-descriptions-item label="创建人">{{ selectedOutbound.created_by }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedOutbound.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ selectedOutbound.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="items-section">
          <h4>商品明细</h4>
          <el-table :data="selectedOutbound.items" style="width: 100%" border>
            <el-table-column prop="product_name" label="商品名称" min-width="250" show-overflow-tooltip />
            <el-table-column prop="product_sku" label="编码" width="150" show-overflow-tooltip />
            <el-table-column prop="batch_no" label="批次" width="150" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="row.batch_no && row.batch_no !== 'NO_BATCH'">{{ row.batch_no }}</span>
                <span v-else class="text-muted">无批次</span>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="出库数量" width="120" align="center" />
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新建/编辑出库单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingOutbound ? '编辑出库单' : '新建出库单'"
      width="1000px"
      @close="resetOutboundForm"
    >
      <el-form :model="outboundForm" :rules="outboundRules" ref="outboundFormRef" label-width="100px">
        <!-- 出库方式选择 -->
        <el-form-item label="出库方式">
          <el-radio-group v-model="outboundForm.outbound_type" @change="onOutboundTypeChange">
            <el-radio value="manual">手动创建</el-radio>
            <el-radio value="from_order">从销售订单创建</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 选择销售订单 -->
        <el-form-item
          v-if="outboundForm.outbound_type === 'from_order'"
          label="销售订单"
          prop="sales_order_id"
        >
          <el-select
            v-model="outboundForm.sales_order_id"
            placeholder="选择销售订单"
            style="width: 100%"
            filterable
            @change="onSalesOrderChange"
            :loading="loadingOrders"
          >
            <el-option
              v-for="order in availableSalesOrders"
              :key="order.id || order.order_no"
              :value="order.id || 0"
              :label="`${order.order_no || '未知订单号'} - ${order.customer_name || '未知客户'}`"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ order.order_no }} - {{ order.customer_name }}</span>
                <span style="color: #8492a6; font-size: 12px;">
                  剩余{{ order.remaining_items?.reduce((sum: number, item: any) => sum + item.quantity, 0) || 0 }}件
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户" prop="customer_id">
              <el-select
                v-model="outboundForm.customer_id"
                placeholder="选择客户"
                style="width: 100%"
                :disabled="outboundForm.outbound_type === 'from_order'"
              >
                <el-option
                  v-for="customer in customers"
                  :key="customer.id"
                  :label="customer.name"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出库日期" prop="outbound_date">
              <el-date-picker
                v-model="outboundForm.outbound_date"
                type="date"
                placeholder="选择出库日期"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售订单号">
              <el-input
                v-model="outboundForm.sales_order_no"
                placeholder="关联的销售订单号（可选）"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注信息">
          <el-input
            v-model="outboundForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 出库商品列表 -->
        <el-form-item label="出库商品" required>
          <div class="outbound-items">
            <div class="items-header">
              <el-button
                type="primary"
                size="small"
                @click="addOutboundItem"
                v-if="outboundForm.outbound_type === 'manual'"
              >
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
              <div v-if="outboundForm.outbound_type === 'from_order'" class="order-mode-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>从销售订单创建模式：商品列表由所选订单自动生成，不可手动添加</span>
              </div>
              <div v-if="outboundForm.outbound_type === 'manual'" class="manual-mode-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>手工创建模式：请按顺序选择商品 → 批次 → 仓库 → 数量</span>
              </div>
            </div>

            <!-- 商品明细表格 -->
            <el-table :data="outboundForm.items" style="width: 100%; margin-top: 10px;" border>

              <el-table-column label="商品名称" min-width="200">
                <template #default="{ row }">
                  <el-select
                    v-model="row.product_id"
                    placeholder="选择商品"
                    filterable
                    @change="onProductChange(row)"
                    :disabled="outboundForm.outbound_type === 'from_order'"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="product in products"
                      :key="product.id"
                      :label="`${product.name || ''} (${product.sku || ''})`"
                      :value="product.id"
                    >
                      <span>{{ product.name || '' }} ({{ product.sku || '' }})</span>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="批次信息" width="150">
                <template #default="{ row }">
                  <el-select
                    v-model="row.batch_no"
                    placeholder="选择批次"
                    style="width: 100%"
                    :disabled="!row.product_id"
                    @change="onBatchChange(row)"
                  >
                    <el-option
                      v-for="batch in getProductBatches(row.product_id)"
                      :key="batch.batch_no || 'no-batch'"
                      :label="batch.batch_no || '无批次'"
                      :value="batch.batch_no || 'NO_BATCH'"
                    >
                      <span>{{ batch.batch_no || '无批次' }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">
                        可用: {{ batch.available_stock }}
                      </span>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="出库仓库" width="150">
                <template #default="{ row }">
                  <el-select
                    v-model="row.warehouse_id"
                    placeholder="选择仓库"
                    style="width: 100%"
                    :disabled="!row.batch_no"
                    @change="onRowWarehouseChange(row)"
                  >
                    <el-option
                      v-for="warehouse in getWarehousesForProductBatch(row.product_id, row.batch_no || '')"
                      :key="warehouse.id || 0"
                      :label="warehouse.name || ''"
                      :value="warehouse.id || 0"
                    >
                      <span>{{ warehouse.name || '' }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">
                        库存: {{ warehouse.available_stock || 0 }}
                      </span>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="出库数量" width="120">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.quantity"
                    :min="1"
                    :max="row.max_quantity || 999"
                    style="width: 100%"
                  />
                  <div v-if="row.available_stock" class="stock-info">
                    可用: {{ row.available_stock }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeOutboundItem($index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 空状态提示 -->
            <div v-if="outboundForm.items.length === 0" class="empty-state">
              <div class="empty-content">
                <el-icon class="empty-icon"><Box /></el-icon>
                <p class="empty-text">暂无商品明细</p>
                <p class="empty-hint" v-if="outboundForm.outbound_type === 'manual'">
                  请点击上方"添加商品"按钮添加出库商品
                </p>
                <p class="empty-hint" v-else>
                  请先选择销售订单，系统将自动生成商品明细
                </p>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveOutbound" :loading="saving">
          {{ editingOutbound ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { salesOutboundApi, type SalesOutbound, type AvailableSalesOrder } from '@/api/sales'
import { customerApi, type Customer } from '@/api/customers'
import { warehouseApi, type Warehouse } from '@/api/warehouses'
import { productApi, type Product } from '@/api/products'
import { inventoryTransferApi, type ProductBatch } from '@/api/inventory'
import {
  Plus,
  Download,
  Tickets,
  Clock,
  Loading,
  CircleCheck,
  Search,
  List,
  Grid,
  ArrowDown,
  Refresh,
  InfoFilled,
  Box
} from '@element-plus/icons-vue'

// 类型定义
interface OutboundStats {
  total_outbounds: number
  pending_outbounds: number
  submitted_outbounds: number
  approved_outbounds: number
  processing_outbounds: number
  completed_outbounds: number
  rejected_outbounds: number
  cancelled_outbounds: number
  partial_outbounds: number
}

interface OutboundForm {
  outbound_type: 'manual' | 'from_order'
  sales_order_id: number | null
  customer_id: number | null
  outbound_date: string | null
  sales_order_no: string
  remark: string
  items: OutboundFormItem[]
}

interface OutboundFormItem {
  sales_order_line_number?: number // 销售订单行号
  product_id: number
  product_name: string
  product_sku: string
  warehouse_id: number
  warehouse_name?: string
  quantity: number
  max_quantity?: number
  batch_no?: string
  available_stock?: number
}

interface ProductBatchInfo {
  productId: number
  batches: ProductBatch[]
}

// 响应式数据
const salesOutbounds = ref<SalesOutbound[]>([])
const customers = ref<Customer[]>([])
const warehouses = ref<Warehouse[]>([])
const products = ref<Product[]>([])
const productBatches = ref<ProductBatchInfo[]>([])
const availableSalesOrders = ref<AvailableSalesOrder[]>([])
const loading = ref(false)
const saving = ref(false)
const loadingOrders = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingOutbound = ref<SalesOutbound | null>(null)
const selectedOutbound = ref<SalesOutbound | null>(null)

// 搜索表单
const searchForm = reactive({
  order_no: '',  // 合并的单号搜索（出库单号或销售订单号）
  customer_id: null as number | null,
  status: '',
  date_range: null as [Date, Date] | null
})

// 统计数据
const outboundStats = ref<OutboundStats>({
  total_outbounds: 0,
  pending_outbounds: 0,
  submitted_outbounds: 0,
  approved_outbounds: 0,
  processing_outbounds: 0,
  completed_outbounds: 0,
  rejected_outbounds: 0,
  cancelled_outbounds: 0,
  partial_outbounds: 0
})

// 表单数据
const outboundForm = reactive<OutboundForm>({
  outbound_type: 'manual',
  sales_order_id: null,
  customer_id: null,
  outbound_date: null,
  sales_order_no: '',
  remark: '',
  items: []
})

// 表单验证规则
const outboundRules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  outbound_date: [
    { required: true, message: '请选择出库日期', trigger: 'change' }
  ],
  sales_order_id: [
    { required: true, message: '请选择销售订单', trigger: 'change' }
  ]
}

// 表单引用
const outboundFormRef = ref()

// 方法
const fetchSalesOutbounds = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    if (searchForm.order_no) {
      // 同时搜索出库单号和销售订单号
      params.outbound_no = searchForm.order_no
      params.sales_order_no = searchForm.order_no
    }
    if (searchForm.customer_id) {
      params.customer_id = searchForm.customer_id
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.date_range && searchForm.date_range.length === 2) {
      params.start_date = searchForm.date_range[0]
      params.end_date = searchForm.date_range[1]
    }

    console.log('📡 发送API请求参数:', params)
    const response = await salesOutboundApi.getSalesOutbounds(params)

    if (response) {
      salesOutbounds.value = response || []
      total.value = response.length || 0
    } else {
      salesOutbounds.value = []
      total.value = 0
    }

    // 获取统计数据 - 只使用真实数据库数据
    try {
      const statsResponse = await salesOutboundApi.getSalesOutboundStats()
      if (statsResponse) {
        outboundStats.value = statsResponse
      } else {
        // 初始化为空统计
        outboundStats.value = {
          total_outbounds: 0,
          pending_outbounds: 0,
          submitted_outbounds: 0,
          approved_outbounds: 0,
          processing_outbounds: 0,
          completed_outbounds: 0,
          rejected_outbounds: 0,
          cancelled_outbounds: 0,
          partial_outbounds: 0
        }
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      // 初始化为空统计，不使用模拟数据
      outboundStats.value = {
        total_outbounds: 0,
        pending_outbounds: 0,
        submitted_outbounds: 0,
        approved_outbounds: 0,
        processing_outbounds: 0,
        completed_outbounds: 0,
        rejected_outbounds: 0,
        cancelled_outbounds: 0,
        partial_outbounds: 0
      }
    }
  } catch (error: any) {
    console.error('获取销售出库单失败:', error)
    ElMessage.error('获取销售出库单失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const fetchCustomers = async () => {
  try {
    const response = await customerApi.getCustomers()
    if (response && (response as any).items) {
      customers.value = (response as any).items
    }
  } catch (error: any) {
    console.error('获取客户列表失败:', error)
  }
}

const handleSearch = () => {
  console.log('🔍 执行搜索，搜索条件', searchForm)
  currentPage.value = 1
  fetchSalesOutbounds()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    order_no: '',
    customer_id: null,
    status: '',
    date_range: null
  })
  handleSearch()
}

const viewOutboundDetail = (outbound: SalesOutbound) => {
  selectedOutbound.value = outbound
  showDetailDialog.value = true
}

const viewSalesOrder = (orderNo: string) => {
  // 跳转到销售订单详情页面
  ElMessage.info(`查看销售订单 ${orderNo}`)
  // TODO: 实现跳转到销售订单详情页面的逻辑
}

const handleOutboundAction = async (command: string, outbound: SalesOutbound) => {
  switch (command) {
    case 'edit':
      editOutbound(outbound)
      break
    case 'submit':
      await submitOutbound(outbound)
      break
    case 'revoke':
      await revokeOutbound(outbound)
      break
    case 'approve':
      await approveOutbound(outbound)
      break
    case 'reject':
      await rejectOutbound(outbound)
      break
    case 'cancel_delivery':
      await cancelDelivery(outbound)
      break
    case 'complete':
      await completeOutbound(outbound)
      break
    case 'delete':
      await deleteOutbound(outbound)
      break
    default:
      ElMessage.warning('未知操作')
  }
}

const submitOutbound = async (outbound: SalesOutbound) => {
  try {
    await ElMessageBox.confirm('确定要提交这个出库单吗？', '确认提交', {
      type: 'warning'
    })

    await salesOutboundApi.submitSalesOutbound(outbound.id!)
    ElMessage.success('出库单提交成功')
    fetchSalesOutbounds()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交出库单失败', error)
      ElMessage.error('提交失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const approveOutbound = async (outbound: SalesOutbound) => {
  try {
    await ElMessageBox.confirm('确定要审核通过这个出库单吗？', '确认审核', {
      type: 'warning'
    })

    await salesOutboundApi.approveSalesOutbound(outbound.id!)
    ElMessage.success('出库单审核通过')
    fetchSalesOutbounds()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核出库单失败', error)
      ElMessage.error('审核失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const rejectOutbound = async (outbound: SalesOutbound) => {
  try {
    const { value: rejectReason } = await ElMessageBox.prompt('请输入拒绝原因', '审核拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '拒绝原因不能为空'
    })

    await salesOutboundApi.rejectSalesOutbound(outbound.id!, rejectReason)
    ElMessage.success('出库单审核拒绝')
    fetchSalesOutbounds()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('拒绝出库单失败', error)
      ElMessage.error('拒绝失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const deleteOutbound = async (outbound: SalesOutbound) => {
  try {
    await ElMessageBox.confirm('确定要删除这个出库单吗？删除后无法恢复！', '确认删除', {
      type: 'warning'
    })

    await salesOutboundApi.deleteSalesOutbound(outbound.id!)
    ElMessage.success('出库单删除成功')
    fetchSalesOutbounds()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除出库单失败', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 撤销出库单（从已提交状态回到草稿状态）
const revokeOutbound = async (outbound: SalesOutbound) => {
  try {
    await ElMessageBox.confirm('确定要撤销这个出库单吗？撤销后将回到草稿状态', '确认撤销', {
      type: 'warning'
    })

    await salesOutboundApi.revokeSalesOutbound(outbound.id!)
    ElMessage.success('出库单撤销成功')
    fetchSalesOutbounds()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('撤销出库单失败', error)
      ElMessage.error('撤销失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 取消发货
const cancelDelivery = async (outbound: SalesOutbound) => {
  try {
    const { value: cancelReason } = await ElMessageBox.prompt('请输入取消发货的原因', '取消发货', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '取消原因不能为空'
    })

    await salesOutboundApi.cancelDelivery(outbound.id!, cancelReason)
    ElMessage.success('发货已取消')
    fetchSalesOutbounds()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('取消发货失败:', error)
      ElMessage.error('取消发货失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 确认完成出库
const completeOutbound = async (outbound: SalesOutbound) => {
  try {
    await ElMessageBox.confirm('确定要标记这个出库单为已完成吗？', '确认完成', {
      type: 'success'
    })

    await salesOutboundApi.completeSalesOutbound(outbound.id!)
    ElMessage.success('出库单已完成')
    fetchSalesOutbounds()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('完成出库单失败', error)
      ElMessage.error('完成失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const exportOutbounds = () => {
  ElMessage.success('出库单导出成功')
}

// 表单相关方法
const resetOutboundForm = () => {
  outboundForm.outbound_type = 'manual'
  outboundForm.sales_order_id = null
  outboundForm.customer_id = null
  outboundForm.outbound_date = null
  outboundForm.sales_order_no = ''
  outboundForm.remark = ''
  outboundForm.items = []
  editingOutbound.value = null

  // 重置表单验证
  if (outboundFormRef.value) {
    outboundFormRef.value.resetFields()
  }
}

const onOutboundTypeChange = (type: 'manual' | 'from_order') => {
  // 切换出库方式时重置相关字段
  outboundForm.sales_order_id = null
  outboundForm.sales_order_no = ''
  outboundForm.items = []

  if (type === 'from_order') {
    // 加载可用的销售订单
    fetchAvailableSalesOrders()
  }
}

const fetchAvailableSalesOrders = async () => {
  loadingOrders.value = true
  try {
    // 获取已审核的销售订单
    const response = await fetch('http://127.0.0.1:8000/api/sales/?status=approved&page_size=100')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    console.log('📋 API返回数据:', data)

    // 处理分页数据，过滤出有剩余未发货数量的订单
    let allOrders = []
    if (data && data.items && Array.isArray(data.items)) {
      allOrders = data.items
    }

    // 过滤出有剩余未发货数量的订单
    const availableOrders = allOrders.filter((order: any) => {
      if (!order.items || !Array.isArray(order.items)) return false

      // 检查是否有剩余未发货的商品
      return order.items.some((item: any) => {
        const shipped = item.shipped_quantity || 0
        const remaining = item.quantity - shipped
        return remaining > 0
      })
    }).map((order: any) => {
      // 只保留有剩余数量的明细
      const remainingItems = order.items.filter((item: any) => {
        const shipped = item.shipped_quantity || 0
        const remaining = item.quantity - shipped
        return remaining > 0
      }).map((item: any) => ({
        ...item,
        quantity: item.quantity - (item.shipped_quantity || 0), // 设置为剩余数量
        original_quantity: item.quantity,
        shipped_quantity: item.shipped_quantity || 0
      }))

      return {
        ...order,
        remaining_items: remainingItems
      }
    })

    availableSalesOrders.value = availableOrders

    console.log('📋 可用销售订单加载成功:', {
      count: availableSalesOrders.value.length,
      orders: availableSalesOrders.value.map((order: any) => ({
        id: order.id,
        order_no: order.order_no,
        customer_name: order.customer_name,
        remaining_items_count: order.remaining_items?.length || 0,
        total_remaining_qty: order.remaining_items?.reduce((sum: number, item: any) => sum + item.quantity, 0) || 0
      }))
    })

    // 详细输出第一个订单的完整数据
    if (availableSalesOrders.value.length > 0) {
      console.log('📋 第一个订单详细数据:', availableSalesOrders.value[0])
    }
  } catch (error) {
    console.error('获取可用销售订单失败', error)
    ElMessage.error('获取可用销售订单失败')
  } finally {
    loadingOrders.value = false
  }
}

const onSalesOrderChange = async (orderId: number) => {
  const selectedOrder = availableSalesOrders.value.find(order => order.id === orderId)
  if (selectedOrder) {
    outboundForm.customer_id = selectedOrder.customer_id
    outboundForm.sales_order_no = selectedOrder.order_no || ''

    // 根据销售订单生成出库商品列表（只包含剩余未发货的商品）
    outboundForm.items = selectedOrder.remaining_items?.map(item => ({
      sales_order_line_number: item.line_number, // 记录销售订单行号
      product_id: item.product_id,
      product_name: item.product_name || '',
      product_sku: item.product_sku || '',
      warehouse_id: 0,
      warehouse_name: '',
      quantity: item.quantity, // 使用剩余未发货数量
      available_stock: 0,
      batch_no: undefined, // 设置为undefined，表示未选择
      max_quantity: item.quantity // 最大数量就是剩余数量
    })) || []

    // 为每个商品获取批次信息
    for (const item of outboundForm.items) {
      if (item.product_id) {
        await fetchProductBatches(item.product_id)
      }
    }

    console.log('✅从销售订单导入商品完成，已获取批次信息')
  }
}

const addOutboundItem = () => {
  outboundForm.items.push({
    sales_order_line_number: undefined, // 手动添加的商品没有行号
    product_id: 0,
    product_name: '',
    product_sku: '',
    warehouse_id: 0,
    warehouse_name: '',
    quantity: 1,
    available_stock: 0,
    batch_no: undefined,
    max_quantity: 0
  })
}

const removeOutboundItem = (index: number) => {
  outboundForm.items.splice(index, 1)
}

// 处理行级仓库变更
const onRowWarehouseChange = (row: OutboundFormItem) => {
  console.log('行级仓库变更:', row.warehouse_id)

  // 设置仓库名称
  const warehouse = warehouses.value.find(w => w.id === row.warehouse_id)
  if (warehouse) {
    row.warehouse_name = warehouse.name || ''
  }

  // 如果已经选择了商品和批次，获取该商品在该仓库该批次的库存信息
  if (row.product_id && row.batch_no && row.warehouse_id) {
    // 从批次信息中获取该仓库的库存数据
    const productBatchInfo = productBatches.value.find(item => item.productId === row.product_id)
    if (productBatchInfo) {
      const batch = productBatchInfo.batches.find(b => (b.batch_no || 'NO_BATCH') === row.batch_no)
      if (batch && batch.warehouses) {
        const warehouseInfo = batch.warehouses.find(w => w.warehouse_id === row.warehouse_id)
        if (warehouseInfo) {
          row.available_stock = warehouseInfo.stock
          row.max_quantity = warehouseInfo.stock
          row.quantity = 1

          console.log(`✅仓库变更：${warehouse?.name}, 可用库存: ${row.available_stock}`)
          return
        }
      }
    }

    // 如果没有找到库存信息，设置为0
    row.available_stock = 0
    row.max_quantity = 0
    row.quantity = 1
    console.log(`⚠️ 仓库变更：${warehouse?.name}, 但未找到库存信息`)
  }
}


const onProductChange = (item: OutboundFormItem) => {
  const product = products.value.find(p => p.id === item.product_id)
  if (product) {
    item.product_name = product.name || ''
    item.product_sku = product.sku || ''

    // 重置批次、仓库选择和数量，因为商品变了
    item.batch_no = undefined
    item.warehouse_id = 0
    item.warehouse_name = ''
    item.quantity = 1
    item.available_stock = 0
    item.max_quantity = 0

    // 获取该商品的所有批次信息（跨所有仓库汇总）
    fetchProductBatches(item.product_id)

    console.log(`✅选择商品: ${product.name || ''}, 请继续选择批次`)
  }
}


const fetchProductBatches = async (productId: number) => {
  try {
    console.log('🔄 获取商品所有批次信息', productId)
    const response = await inventoryTransferApi.getProductAllBatches(productId)

    // 将批次信息存储到productBatches中，按商品ID分组
    const existingIndex = productBatches.value.findIndex(item => item.productId === productId)
    if (existingIndex >= 0) {
      productBatches.value[existingIndex] = { productId, batches: response || [] }
    } else {
      productBatches.value.push({ productId, batches: response || [] })
    }

    console.log('✅获取到批次信息', response?.length || 0, '个')
  } catch (error: any) {
    console.error('❌获取批次信息失败:', error)
    ElMessage.error('获取批次信息失败: ' + (error.response?.data?.detail || error.message))
  }
}

const getProductBatches = (productId: number): ProductBatch[] => {
  const productBatchInfo = productBatches.value.find(item => item.productId === productId)
  return productBatchInfo?.batches || []
}

const getWarehousesForProductBatch = (productId: number, batchNo: string): any[] => {
  if (!productId) {
    return []
  }

  // 从批次信息中获取有该批次库存的仓库
  const productBatchInfo = productBatches.value.find(item => item.productId === productId)
  if (!productBatchInfo) {
    return []
  }

  // 对于无批次的情况，batchNo可能是'NO_BATCH'，需要特殊处理
  const batch = productBatchInfo.batches.find(b => {
    const batchNoValue = b.batch_no || 'NO_BATCH'
    return batchNoValue === batchNo
  })

  if (!batch || !batch.warehouses) {
    return []
  }

  // 返回有该批次库存的仓库，并添加库存信息
  return batch.warehouses.map(warehouseInfo => ({
    id: warehouseInfo.warehouse_id,
    name: warehouseInfo.warehouse_name,
    available_stock: warehouseInfo.stock,
    is_active: true
  }))
}

const onBatchChange = (item: OutboundFormItem) => {
  // 重置仓库选择，因为批次变更
  item.warehouse_id = 0
  item.warehouse_name = ''
  item.available_stock = 0
  item.quantity = 1
  item.max_quantity = 0

  console.log(`✅选择批次: ${item.batch_no === 'NO_BATCH' ? '无批次' : (item.batch_no || '无批次')}, 请继续选择仓库`)
}





const editOutbound = async (outbound: SalesOutbound) => {
  editingOutbound.value = outbound

  console.log('🔧 开始编辑出库单:', outbound)
  console.log('🔧 出库单明细:', outbound.items)

  // 填充表单数据
  outboundForm.outbound_type = outbound.sales_order_id ? 'from_order' : 'manual'
  outboundForm.sales_order_id = outbound.sales_order_id || null
  outboundForm.customer_id = outbound.customer_id
  outboundForm.outbound_date = outbound.outbound_date || null
  outboundForm.sales_order_no = outbound.sales_order_no || ''
  outboundForm.remark = outbound.remark || ''
  outboundForm.items = outbound.items?.map(item => ({
    product_id: item.product_id,
    product_name: item.product_name || '',
    product_sku: item.product_sku || '',
    warehouse_id: item.warehouse_id || 0,
    warehouse_name: item.warehouse_name || '',
    quantity: item.quantity,
    available_stock: 0,
    batch_no: item.batch_no || 'NO_BATCH', // 如果批次号为空或null，使用 'NO_BATCH'
    max_quantity: item.quantity // 编辑时最大数量就是当前数量
  })) || []

  // 为每个商品获取批次信息并设置库存信息
  for (const item of outboundForm.items) {
    if (item.product_id) {
      await fetchProductBatches(item.product_id)

      // 设置当前批次和仓库的库存信息
      if (item.batch_no && item.warehouse_id) {
        const productBatchInfo = productBatches.value.find(info => info.productId === item.product_id)
        if (productBatchInfo) {
          const batch = productBatchInfo.batches.find(b => (b.batch_no || 'NO_BATCH') === item.batch_no)
          if (batch && batch.warehouses) {
            const warehouseInfo = batch.warehouses.find(w => w.warehouse_id === item.warehouse_id)
            if (warehouseInfo) {
              item.available_stock = warehouseInfo.stock
              item.max_quantity = warehouseInfo.stock + item.quantity // 当前库存 + 已出库数量
            }
          }
        }
      }
    }
  }

  // 如果是从订单创建的出库单，需要加载可用订单列表
  if (outboundForm.outbound_type === 'from_order') {
    await fetchAvailableSalesOrders()

    // 如果当前选中的订单不在可用列表中，需要单独获取并添加到列表中
    if (outbound.sales_order_id && !availableSalesOrders.value.find(order => order.id === outbound.sales_order_id)) {
      try {
        // 获取当前订单的详细信息
        const response = await fetch(`http://127.0.0.1:8000/api/sales/${outbound.sales_order_id}`)
        if (response.ok) {
          const orderData = await response.json()
          // 添加到可用订单列表中，以便下拉框能显示
          availableSalesOrders.value.unshift({
            id: orderData.id,
            order_no: orderData.order_no,
            customer_id: orderData.customer_id,
            customer_name: orderData.customer_name,
            total_amount: orderData.total_amount,
            status: orderData.status,
            delivery_date: orderData.delivery_date,
            created_at: orderData.created_at,
            remaining_items: [] // 编辑时不需要剩余明细
          })
        }
      } catch (error) {
        console.warn('获取当前订单信息失败:', error)
      }
    }
  }

  showCreateDialog.value = true
}

const saveOutbound = async () => {
  if (!outboundFormRef.value) return

  // 准备发送的数据
  let outboundData: any = null

  try {
    // 验证表单
    await outboundFormRef.value.validate()

    // 验证商品列表
    if (outboundForm.items.length === 0) {
      ElMessage.error('请至少添加一个出库商品')
      return
    }

    // 验证商品是否都已选择
    const invalidItems = outboundForm.items.filter(item =>
      !item.product_id ||
      !item.batch_no ||
      !item.warehouse_id ||
      item.quantity <= 0
    )
    if (invalidItems.length > 0) {
      ElMessage.error('请完善商品信息：选择商品、批次、仓库并设置数量')
      return
    }

    saving.value = true

    // 准备发送的数据
    outboundData = {
      sales_order_id: outboundForm.sales_order_id || undefined,
      customer_id: outboundForm.customer_id!,
      outbound_date: outboundForm.outbound_date ? new Date(outboundForm.outbound_date).toISOString() : undefined,
      sales_order_no: outboundForm.sales_order_no || undefined,
      remark: outboundForm.remark || undefined,
      status: 'pending' as any,
      operator: undefined,
      items: outboundForm.items
        .filter(item => item.product_id && item.warehouse_id)
        .map(item => ({
          sales_order_line_number: item.sales_order_line_number || undefined,
          product_id: item.product_id,
          warehouse_id: item.warehouse_id,
          batch_no: item.batch_no === 'NO_BATCH' ? null : item.batch_no,
          quantity: item.quantity
        }))
    }

    console.log('发送的出库单数据', outboundData)

    if (editingOutbound.value) {
      // 更新出库单
      await salesOutboundApi.updateSalesOutbound(editingOutbound.value.id!, outboundData)
      ElMessage.success('出库单更新成功')
    } else {
      // 创建出库单
      await salesOutboundApi.createSalesOutbound(outboundData)
      ElMessage.success('出库单创建成功')
    }

    showCreateDialog.value = false
    fetchSalesOutbounds()
  } catch (error: any) {
    console.error('保存出库单失败', error)
    console.error('错误详情:', error.response?.data)
    console.error('详细错误信息:', JSON.stringify(error.response?.data, null, 2))
    console.error('发送的数据:', JSON.stringify(outboundData, null, 2))

    // 显示详细的验证错误
    if (error.response?.data?.detail && Array.isArray(error.response.data.detail)) {
      const errorMessages = error.response.data.detail.map((err: any) =>
        `${err.loc?.join('.')} - ${err.msg}`
      ).join('\n')
      console.error('验证错误详情:', errorMessages)
      ElMessage.error('验证失败:\n' + errorMessages)
    } else {
      ElMessage.error('保存失败: ' + (error.response?.data?.detail || error.message))
    }
  } finally {
    saving.value = false
  }
}

const fetchWarehouses = async () => {
  try {
    const response = await warehouseApi.getWarehouses()
    console.log('仓库API响应:', response)

    // 过滤出有库存的仓库（可用库存 > 0）
    const warehousesWithStock = (response || []).filter((warehouse: any) => {
      const availableStock = warehouse.available_stock || 0
      const totalInventoryQuantity = warehouse.total_inventory_quantity || 0

      console.log(`仓库 ${warehouse.name || '未知'}: 可用库存=${availableStock}, 总库存=${totalInventoryQuantity}, 激活状态=${warehouse.is_active}`)

      // 仓库必须是激活状态，且有可用库存
      return warehouse.is_active && (availableStock > 0 || totalInventoryQuantity > 0)
    })

    warehouses.value = warehousesWithStock
    console.log(`过滤后的仓库数量: ${warehousesWithStock.length}/${(response || []).length}`)

    if (warehousesWithStock.length === 0) {
      ElMessage.warning('当前没有有库存的仓库可用于出库')
    }
  } catch (error) {
    console.error('获取仓库列表失败:', error)
    warehouses.value = []
    ElMessage.error('获取仓库列表失败')
  }
}

const fetchProducts = async () => {
  try {
    console.log('🔄 开始获取商品列表..')
    const response = await productApi.getProducts()
    console.log('📦 商品API完整响应:', response)
    console.log('📊 响应数据类型:', typeof response)
    console.log('📋 响应数据内容:', response)

    if (response && response.items) {
      products.value = response.items
      if (response.items.length === 0) {
        ElMessage.warning('当前没有商品数据，请先添加商品')
      }
    } else {
      products.value = []
      ElMessage.error('商品API响应异常')
    }
  } catch (error: any) {
    products.value = []
    ElMessage.error('获取商品列表失败: ' + (error.response?.data?.detail || error.message))
  }
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝',
    partial: '部分出库',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    submitted: 'warning',
    approved: 'success',
    rejected: 'danger',
    partial: 'warning',
    completed: 'success',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getWarehouseNames = (items: any[]) => {
  const warehouseNames = [...new Set(items.map(item => item.warehouse_name || '').filter(name => name))]
  if (warehouseNames.length === 0) return '-'
  if (warehouseNames.length === 1) return warehouseNames[0] || '-'
  return `${warehouseNames[0] || '未知'} 等${warehouseNames.length}个仓库`
}

// 根据客户ID获取客户名称
const getCustomerName = (customerId: number): string => {
  const customer = customers.value.find(c => c.id === customerId)
  return customer?.name || `客户ID: ${customerId}`
}

const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchSalesOutbounds()
  fetchCustomers()
  fetchWarehouses()
  fetchProducts()
})
</script>

<style scoped>
.sales-outbound-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 出库单概览 */
.outbound-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.pending {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.processing {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.completed {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 12px;
}

.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
}

.card-view {
  padding: 20px;
}

.outbound-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.card-content {
  margin-bottom: 16px;
}

.card-content p {
  margin: 8px 0;
  color: #606266;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.outbound-detail {
  padding: 0;
}

.items-section {
  margin-top: 24px;
}

.items-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

:deep(.el-descriptions) {
  margin-bottom: 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.outbounds-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.outbound-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.outbound-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.outbound-card .card-header {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.outbound-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.outbound-info p {
  margin: 0 0 2px 0;
  font-size: 12px;
  opacity: 0.9;
}

.outbound-card .card-content {
  padding: 16px;
}

.outbound-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-row .label {
  color: #909399;
  font-weight: 500;
}

.detail-row .value {
  color: #303133;
  font-weight: 600;
}

.card-actions {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

/* 表单样式 */
.outbound-items {
  width: 100%;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.order-mode-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 13px;
}

.manual-mode-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-size: 13px;
  background-color: #ecf5ff;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.custom-table {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  min-width: 800px;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.table-body {
  background: white;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.table-row:last-child {
  border-bottom: none;
}

.header-cell,
.table-cell {
  padding: 12px;
  border-right: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
}

.header-cell:last-child,
.table-cell:last-child {
  border-right: none;
}

.header-cell {
  font-weight: 600;
  color: #303133;
  background: #f5f7fa;
}

.table-cell {
  background: white;
}

.product-name {
  flex: 2;
  min-width: 200px;
}

.warehouse {
  flex: 1.2;
  min-width: 150px;
}

.batch-info {
  flex: 1;
  min-width: 120px;
}

.quantity {
  flex: 1;
  min-width: 120px;
}

.actions {
  flex: 0 0 100px;
  justify-content: center;
}

.selected-product {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 100%;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-name-text {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.product-sku-text {
  color: #909399;
  font-size: 12px;
}

.stock-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  background-color: #fafafa;
  border-radius: 4px;
  margin: 10px 0;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.empty-text {
  font-size: 16px;
  color: #606266;
  margin: 0;
  font-weight: 500;
}

.empty-hint {
  font-size: 14px;
  color: #909399;
  margin: 0;
  line-height: 1.4;
}

.quantity-row {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.stock-display {
  flex-shrink: 0;
}

.quantity-input {
  flex: 1;
  min-width: 80px;
}

.stock-badge {
  display: inline-block;
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #91d5ff;
  transition: all 0.3s;
}

.stock-badge.stock-warning {
  background: #fff7e6;
  color: #fa8c16;
  border-color: #ffd591;
}

.stock-badge.stock-danger {
  background: #fff2f0;
  color: #ff4d4f;
  border-color: #ffccc7;
}

.error-info {
  margin-top: 2px;
  text-align: center;
}

.error-badge {
  display: inline-block;
  background: #fff2f0;
  color: #ff4d4f;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #ffccc7;
  animation: shake 0.5s ease-in-out;
}

/* 详情对话框样式 */
.outbound-detail {
  .items-section {
    margin-top: 20px;

    h4 {
      margin-bottom: 16px;
      color: #303133;
      font-weight: 600;
    }
  }

  .text-muted {
    color: #909399;
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .outbound-overview .el-col {
    margin-bottom: 16px;
  }

  .outbounds-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .sales-outbound-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .outbound-overview .el-col {
    margin-bottom: 12px;
  }

  .overview-content {
    padding: 16px;
  }

  .outbounds-grid {
    grid-template-columns: 1fr;
  }

  .search-card .el-form {
    flex-direction: column;
  }

  .search-card .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }

  .card-view .el-col {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-left h2 {
    font-size: 24px;
  }

  .overview-value {
    font-size: 20px;
  }

  .overview-content {
    gap: 12px;
  }

  .overview-icon {
    padding: 8px;
  }
}
</style>