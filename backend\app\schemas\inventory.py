"""
库存数据模式
"""

from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel, Field
from enum import Enum


class InventoryStatus(str, Enum):
    """库存状态"""
    NORMAL = "normal"
    LOCKED = "locked"
    DAMAGED = "damaged"


class TransactionType(str, Enum):
    """库存变动类型"""
    IN = "in"           # 入库
    OUT = "out"         # 出库
    ADJUST = "adjust"   # 调整
    TRANSFER = "transfer"  # 调拨


class InventoryBase(BaseModel):
    """库存基础模型"""
    product_id: int = Field(..., description="商品ID")
    warehouse_id: int = Field(..., description="仓库ID")
    current_stock: int = Field(default=0, description="当前库存")
    reserved_stock: int = Field(default=0, description="预留库存")
    min_stock: int = Field(default=0, description="最小库存")
    max_stock: int = Field(default=0, description="最大库存")
    safety_stock: int = Field(default=0, description="安全库存")
    average_cost: float = Field(default=0.0, description="平均成本")
    last_cost: float = Field(default=0.0, description="最新成本")
    location: Optional[str] = Field(None, description="库位")
    zone: Optional[str] = Field(None, description="库区")
    batch_no: Optional[str] = Field(None, description="批次号")
    production_date: Optional[date] = Field(None, description="生产日期")
    expiry_date: Optional[date] = Field(None, description="到期日期")
    status: InventoryStatus = Field(default=InventoryStatus.NORMAL, description="状态")
    is_active: bool = Field(default=True, description="是否启用")


class InventoryCreate(InventoryBase):
    """创建库存"""
    pass


class InventoryUpdate(BaseModel):
    """更新库存"""
    current_stock: Optional[int] = None
    reserved_stock: Optional[int] = None
    min_stock: Optional[int] = None
    max_stock: Optional[int] = None
    safety_stock: Optional[int] = None
    average_cost: Optional[float] = None
    last_cost: Optional[float] = None
    location: Optional[str] = None
    zone: Optional[str] = None
    batch_no: Optional[str] = None
    production_date: Optional[date] = None
    expiry_date: Optional[date] = None
    status: Optional[InventoryStatus] = None
    is_active: Optional[bool] = None


class InventoryResponse(InventoryBase):
    """库存响应"""
    id: int
    available_stock: int = Field(..., description="可用库存")
    product_name: str = Field(..., description="商品名称")
    product_sku: str = Field(..., description="商品SKU")
    product_category: Optional[str] = Field(None, description="商品分类")
    product_image: Optional[str] = Field(None, description="商品图片")
    warehouse_name: str = Field(..., description="仓库名称")
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_in_date: Optional[datetime] = None
    last_out_date: Optional[datetime] = None

    class Config:
        from_attributes = True


class InventoryQuery(BaseModel):
    """库存查询参数"""
    product_name: Optional[str] = Field(None, description="商品名称")
    product_sku: Optional[str] = Field(None, description="商品SKU")
    product_category: Optional[str] = Field(None, description="商品分类")
    warehouse_id: Optional[int] = Field(None, description="仓库ID")
    batch_no: Optional[str] = Field(None, description="批次号")
    production_date_start: Optional[date] = Field(None, description="生产日期开始")
    production_date_end: Optional[date] = Field(None, description="生产日期结束")
    expiry_date_start: Optional[date] = Field(None, description="到期日期开始")
    expiry_date_end: Optional[date] = Field(None, description="到期日期结束")
    is_expired: Optional[bool] = Field(None, description="是否过期")
    is_near_expiry: Optional[bool] = Field(None, description="是否临近过期")
    status: Optional[InventoryStatus] = Field(None, description="库存状态")
    stock_status: Optional[str] = Field(None, description="库存水平: sufficient, low, out")
    exclude_zero_stock: Optional[bool] = Field(False, description="排除零库存商品")
    min_stock_level: Optional[int] = Field(None, description="最小库存水平")
    max_stock_level: Optional[int] = Field(None, description="最大库存水平")


class InventoryAdjustment(BaseModel):
    """库存调整"""
    inventory_id: int = Field(..., description="库存ID")
    adjustment_type: TransactionType = Field(..., description="调整类型")
    quantity: int = Field(..., description="调整数量")
    reason: str = Field(..., description="调整原因")
    remark: Optional[str] = Field(None, description="备注")
    operator: str = Field(..., description="操作人")


class InventoryTransactionBase(BaseModel):
    """库存变动记录基础模型"""
    inventory_id: int = Field(..., description="库存ID")
    transaction_type: TransactionType = Field(..., description="变动类型")
    quantity: int = Field(..., description="变动数量")
    before_quantity: int = Field(..., description="变动前数量")
    after_quantity: int = Field(..., description="变动后数量")
    reference_type: Optional[str] = Field(None, description="关联单据类型")
    reference_id: Optional[int] = Field(None, description="关联单据ID")
    reference_no: Optional[str] = Field(None, description="关联单据号")
    operator: str = Field(..., description="操作人")
    reason: Optional[str] = Field(None, description="变动原因")
    remark: Optional[str] = Field(None, description="备注")


class InventoryTransactionCreate(InventoryTransactionBase):
    """创建库存变动记录"""
    pass


class InventoryTransactionResponse(InventoryTransactionBase):
    """库存变动记录响应"""
    id: int
    product_name: str = Field(..., description="商品名称")
    product_sku: str = Field(..., description="商品SKU")
    warehouse_name: str = Field(..., description="仓库名称")
    created_at: datetime

    class Config:
        from_attributes = True


class InventoryStats(BaseModel):
    """库存统计"""
    total_products: int = Field(..., description="商品总数")
    total_warehouses: int = Field(..., description="仓库总数")
    normal_stock: int = Field(..., description="库存正常商品数")
    low_stock: int = Field(..., description="库存不足商品数")
    out_of_stock: int = Field(..., description="缺货商品数")
    total_stock_value: float = Field(..., description="库存总价值")
    average_stock_level: float = Field(..., description="平均库存水平")





class PaginatedInventoryResponse(BaseModel):
    """分页库存响应"""
    items: List[InventoryResponse]
    total: int
    page: int
    page_size: int
    pages: int


class PaginatedTransactionResponse(BaseModel):
    """分页库存变动记录响应"""
    items: List[InventoryTransactionResponse]
    total: int
    page: int
    page_size: int
    pages: int


# ==================== 库存调拨单相关Schema ====================

class InventoryTransferStatus(str, Enum):
    """库存调拨单状态"""
    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    TRANSFERRED = "transferred"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class InventoryTransferItemBase(BaseModel):
    """库存调拨单明细基础模型"""
    product_id: int = Field(..., description="商品ID")
    product_name: str = Field(..., description="商品名称")
    product_sku: Optional[str] = Field(None, description="商品SKU")
    transfer_quantity: int = Field(..., gt=0, description="调拨数量")
    available_quantity: Optional[int] = Field(None, description="可用库存数量")
    batch_no: Optional[str] = Field(None, description="批次号")


class InventoryTransferItemCreate(InventoryTransferItemBase):
    """创建库存调拨单明细"""
    pass


class InventoryTransferItemUpdate(BaseModel):
    """更新库存调拨单明细"""
    product_id: Optional[int] = None
    product_name: Optional[str] = None
    product_sku: Optional[str] = None
    transfer_quantity: Optional[int] = Field(None, gt=0)
    available_quantity: Optional[int] = None
    batch_no: Optional[str] = None


class InventoryTransferItem(InventoryTransferItemBase):
    """库存调拨单明细"""
    id: int
    inventory_transfer_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class InventoryTransferBase(BaseModel):
    """库存调拨单基础模型"""
    transfer_no: Optional[str] = Field(None, description="调拨单号")
    from_warehouse_id: int = Field(..., description="调出仓库ID")
    to_warehouse_id: int = Field(..., description="调入仓库ID")
    transfer_date: datetime = Field(..., description="调拨日期")
    reason: str = Field(..., description="调拨原因")
    total_quantity: int = Field(..., ge=0, description="调拨总数量")
    remark: Optional[str] = Field(None, description="备注")


class InventoryTransferCreate(InventoryTransferBase):
    """创建库存调拨单"""
    items: List[InventoryTransferItemCreate] = Field(..., description="调拨明细")


class InventoryTransferUpdate(BaseModel):
    """更新库存调拨单"""
    transfer_no: Optional[str] = None
    from_warehouse_id: Optional[int] = None
    to_warehouse_id: Optional[int] = None
    transfer_date: Optional[datetime] = None
    reason: Optional[str] = None
    total_quantity: Optional[int] = Field(None, ge=0)
    remark: Optional[str] = None
    items: Optional[List[InventoryTransferItemCreate]] = None


class InventoryTransferStatusUpdate(BaseModel):
    """更新库存调拨单状态"""
    status: InventoryTransferStatus = Field(..., description="新状态")
    note: Optional[str] = Field(None, description="操作备注")


class InventoryTransfer(InventoryTransferBase):
    """库存调拨单"""
    id: int
    status: InventoryTransferStatus

    # 审核信息
    submitted_at: Optional[datetime] = None
    submitted_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    approved_by: Optional[str] = None
    approval_note: Optional[str] = None

    # 调拨信息
    transferred_at: Optional[datetime] = None
    transferred_by: Optional[str] = None

    # 时间戳
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    # 关联信息
    from_warehouse_name: Optional[str] = None
    to_warehouse_name: Optional[str] = None

    # 明细
    items: List[InventoryTransferItem] = []

    class Config:
        from_attributes = True


class InventoryTransferStats(BaseModel):
    """库存调拨单统计"""
    total_transfers: int = Field(0, description="调拨单总数")
    draft_transfers: int = Field(0, description="草稿数量")
    submitted_transfers: int = Field(0, description="已提交数量")
    approved_transfers: int = Field(0, description="已审核数量")
    transferred_transfers: int = Field(0, description="已调拨数量")
    completed_transfers: int = Field(0, description="已完成数量")
    total_quantity: int = Field(0, description="调拨总数量")


class InventoryTransferQuery(BaseModel):
    """库存调拨单查询参数"""
    transfer_no: Optional[str] = Field(None, description="调拨单号")
    from_warehouse_id: Optional[int] = Field(None, description="调出仓库ID")
    to_warehouse_id: Optional[int] = Field(None, description="调入仓库ID")
    status: Optional[InventoryTransferStatus] = Field(None, description="状态")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")











