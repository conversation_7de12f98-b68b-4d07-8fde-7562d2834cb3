import api from './index'

export interface Product {
  id: number
  name: string
  sku?: string
  brand?: string
  category: string
  price: number
  cost: number
  stock: number
  description?: string
  specifications?: string
  image?: string
  status: 'active' | 'inactive' | 'discontinued'
  is_active: boolean
  sales_data?: any
  created_at: string
  updated_at?: string
}

export interface ProductListResponse {
  items: Product[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface ProductQuery {
  name?: string
  category?: string
  brand?: string
  status?: string
  min_price?: number
  max_price?: number
  low_stock?: boolean
  page?: number
  page_size?: number
}



// 产品API
export const productApi = {
  // 获取产品列表
  getProducts: async (params?: ProductQuery): Promise<ProductListResponse> => {
    return await api.get('/api/products/', { params })
  },

  // 获取单个产品
  getProduct: async (id: number): Promise<Product> => {
    return await api.get(`/api/products/${id}`)
  },

  // 创建产品
  createProduct: async (data: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<Product> => {
    return await api.post('/api/products/', data)
  },

  // 更新产品
  updateProduct: async (id: number, data: Partial<Product>): Promise<Product> => {
    return await api.put(`/api/products/${id}`, data)
  },

  // 删除产品
  deleteProduct: async (id: number): Promise<any> => {
    return await api.delete(`/api/products/${id}`)
  },


}