import api from './index'

// 平台类型枚举
export enum PlatformType {
  TAOBAO = 'taobao',
  TMALL = 'tmall',
  JD = 'jd',
  PDD = 'pdd',
  DOUYIN = 'douyin',
  KUAISHOU = 'kuaishou',
  WECHAT = 'wechat',
  CUSTOM = 'custom'
}

// API类型枚举
export enum APIType {
  INVENTORY_QUERY = 'inventory_query',
  INVENTORY_UPDATE = 'inventory_update',
  ORDER_LIST = 'order_list',
  ORDER_DETAIL = 'order_detail',
  PRODUCT_LIST = 'product_list',
  PRODUCT_DETAIL = 'product_detail'
}

// 电商平台API配置接口
export interface PlatformAPI {
  id: number
  platform_type: PlatformType
  platform_name: string
  api_type: APIType
  api_name: string
  api_url: string
  api_method: string
  api_headers?: string
  api_params?: string
  api_auth?: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at?: string
}

// 创建电商平台API配置接口
export interface PlatformAPICreate {
  platform_type: PlatformType
  platform_name: string
  api_type: APIType
  api_name: string
  api_url: string
  api_method?: string
  api_headers?: string
  api_params?: string
  api_auth?: string
  description?: string
  is_active?: boolean
}

// 更新电商平台API配置接口
export interface PlatformAPIUpdate {
  platform_type?: PlatformType
  platform_name?: string
  api_type?: APIType
  api_name?: string
  api_url?: string
  api_method?: string
  api_headers?: string
  api_params?: string
  api_auth?: string
  description?: string
  is_active?: boolean
}

// 查询参数接口
export interface PlatformAPIQuery {
  platform_type?: PlatformType
  api_type?: APIType
  platform_name?: string
  api_name?: string
  is_active?: boolean
  page?: number
  page_size?: number
}

// 列表响应接口
export interface PlatformAPIListResponse {
  items: PlatformAPI[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 选项信息接口
export interface APITypeInfo {
  value: string
  label: string
  description: string
}

export interface PlatformTypeInfo {
  value: string
  label: string
  description: string
}

export interface PlatformAPIOptions {
  api_types: APITypeInfo[]
  platform_types: PlatformTypeInfo[]
  api_methods: string[]
}

// 电商平台API管理API
export const platformApiApi = {
  // 获取配置选项
  getOptions: async (): Promise<PlatformAPIOptions> => {
    return await api.get('/api/platform-apis/options')
  },

  // 获取电商平台API配置列表
  getPlatformAPIs: async (params?: PlatformAPIQuery): Promise<PlatformAPIListResponse> => {
    return await api.get('/api/platform-apis/', { params })
  },

  // 获取单个电商平台API配置
  getPlatformAPI: async (id: number): Promise<PlatformAPI> => {
    return await api.get(`/api/platform-apis/${id}`)
  },

  // 创建电商平台API配置
  createPlatformAPI: async (data: PlatformAPICreate): Promise<PlatformAPI> => {
    return await api.post('/api/platform-apis/', data)
  },

  // 更新电商平台API配置
  updatePlatformAPI: async (id: number, data: PlatformAPIUpdate): Promise<PlatformAPI> => {
    return await api.put(`/api/platform-apis/${id}`, data)
  },

  // 删除电商平台API配置
  deletePlatformAPI: async (id: number): Promise<any> => {
    return await api.delete(`/api/platform-apis/${id}`)
  },

  // 切换启用状态
  togglePlatformAPIStatus: async (id: number): Promise<any> => {
    return await api.post(`/api/platform-apis/${id}/toggle`)
  }
}
