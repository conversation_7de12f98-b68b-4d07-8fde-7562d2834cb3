<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from './stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ShoppingBag,
  HomeFilled,
  Box,
  Van,
  User,
  ArrowDown,
  Setting,
  SwitchButton,
  TrendCharts,
  DataAnalysis,
  MapLocation,
  Document,
  Tools,
  Star,
  Files,
  OfficeBuilding,
  Search,
  ShoppingCart,
  Tickets,
  Sell,
  Upload,
  RefreshLeft,
  RefreshRight,
  Switch
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const activeIndex = ref('/')
const isCollapsed = ref(false)

// 计算属性
const showNavigation = computed(() => {
  return !route.meta?.hideNavigation && authStore.isLoggedIn
})

const handleSelect = (key: string) => {
  activeIndex.value = key
  router.push(key)
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const result = await authStore.logout()
    if (result.success) {
      ElMessage.success('已退出登录')
      router.push('/login')
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

onMounted(() => {
  // 初始化认证状态
  authStore.initializeAuth()

  // 设置当前激活的菜单项
  activeIndex.value = route.path
})
</script>

<template>
  <div class="app-container">
    <!-- 登录页面 -->
    <router-view v-if="!showNavigation" />

    <!-- 主应用布局 -->
    <div v-else class="main-layout">
      <!-- 左侧导航栏 -->
      <div class="sidebar" :class="{ 'collapsed': isCollapsed }">
        <!-- Logo区域 -->
        <div class="sidebar-header">
          <div class="logo-section">
            <el-icon size="32" color="#ffffff">
              <ShoppingBag />
            </el-icon>
            <h1 v-if="!isCollapsed" class="logo-title">电子商务决策系统</h1>
          </div>
          <el-button
            class="collapse-btn"
            circle
            size="small"
            @click="toggleSidebar"
          >
            <el-icon>
              <ArrowDown :class="{ 'rotate': isCollapsed }" />
            </el-icon>
          </el-button>
        </div>

        <!-- 导航菜单 -->
        <el-menu
          :default-active="activeIndex"
          class="sidebar-menu"
          :collapse="isCollapsed"
          @select="handleSelect"
        >
          <el-menu-item index="/">
            <el-icon><HomeFilled /></el-icon>
            <template #title>首页</template>
          </el-menu-item>

          <el-sub-menu index="purchase">
            <template #title>
              <el-icon><ShoppingCart /></el-icon>
              <span>采购管理</span>
            </template>
            <el-menu-item index="/purchase/orders">
              <el-icon><Document /></el-icon>
              <template #title>采购订单</template>
            </el-menu-item>
            <el-menu-item index="/purchase/receipt">
              <el-icon><Tickets /></el-icon>
              <template #title>采购入库单</template>
            </el-menu-item>
            <el-menu-item index="/purchase/returns">
              <el-icon><RefreshLeft /></el-icon>
              <template #title>采购退货单</template>
            </el-menu-item>
            <el-menu-item index="/purchase/suppliers">
              <el-icon><OfficeBuilding /></el-icon>
              <template #title>供应商档案</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="sales">
            <template #title>
              <el-icon><Sell /></el-icon>
              <span>销售管理</span>
            </template>
            <el-menu-item index="/sales/orders">
              <el-icon><Document /></el-icon>
              <template #title>销售订单</template>
            </el-menu-item>
            <el-menu-item index="/sales/outbound">
              <el-icon><Upload /></el-icon>
              <template #title>销售出库单</template>
            </el-menu-item>
            <el-menu-item index="/sales/returns">
              <el-icon><RefreshRight /></el-icon>
              <template #title>销售退货单</template>
            </el-menu-item>
            <el-menu-item index="/sales/customers">
              <el-icon><User /></el-icon>
              <template #title>客户档案</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="inventory">
            <template #title>
              <el-icon><Box /></el-icon>
              <span>库存管理</span>
            </template>
            <el-menu-item index="/inventory/query">
              <el-icon><Search /></el-icon>
              <template #title>库存查询</template>
            </el-menu-item>
            <el-menu-item index="/inventory/transfers">
              <el-icon><Switch /></el-icon>
              <template #title>库存调拨单</template>
            </el-menu-item>
            <el-menu-item index="/warehouses">
              <el-icon><OfficeBuilding /></el-icon>
              <template #title>仓库管理</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="logistics">
            <template #title>
              <el-icon><Van /></el-icon>
              <span>物流配送</span>
            </template>
            <el-menu-item index="/logistics">
              <el-icon><Van /></el-icon>
              <template #title>物流总览</template>
            </el-menu-item>
            <el-menu-item index="/logistics/routes">
              <el-icon><MapLocation /></el-icon>
              <template #title>配送路线</template>
            </el-menu-item>
            <el-menu-item index="/logistics/orders">
              <el-icon><Document /></el-icon>
              <template #title>配送订单</template>
            </el-menu-item>
            <el-menu-item index="/logistics/cost-calculator">
              <el-icon><Tools /></el-icon>
              <template #title>成本计算</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/settings/products">
              <el-icon><ShoppingBag /></el-icon>
              <template #title>商品档案</template>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
          <div class="navbar-left">
            <!-- 可以放置面包屑导航或其他内容 -->
          </div>
          <div class="navbar-right">
            <!-- 用户信息区域 -->
            <div v-if="authStore.isLoggedIn" class="user-section">
              <el-dropdown @command="handleUserCommand" placement="bottom-end">
                <div class="user-info">
                  <el-avatar
                    :src="authStore.user?.avatar"
                    :size="32"
                    class="user-avatar"
                  >
                    <el-icon><User /></el-icon>
                  </el-avatar>
                  <div class="user-details">
                    <span class="username">{{ authStore.user?.username }}</span>
                    <span class="user-role">管理员</span>
                  </div>
                  <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">
                      <el-icon><User /></el-icon>
                      个人中心
                    </el-dropdown-item>
                    <el-dropdown-item command="settings">
                      <el-icon><Setting /></el-icon>
                      系统设置
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <el-icon><SwitchButton /></el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <div class="content-wrapper">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  margin: 0;
  padding: 0;
}

#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.app-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
  position: relative;
}

/* 主应用布局 */
.main-layout {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  overflow: hidden;
  margin: 0;
  padding: 0;
  background: #ffffff;
  left: 0;
  top: 0;
}



/* 左侧导航栏 */
.sidebar {
  position: relative;
  z-index: 10;
  width: 260px;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;
  margin: 0;
  padding: 0;
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-title {
  font-size: 1.2rem;
  font-weight: 700;
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;
}

.collapse-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.3s ease;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.collapse-btn .el-icon {
  transition: transform 0.3s ease;
}

.collapse-btn .el-icon.rotate {
  transform: rotate(90deg);
}

/* 导航菜单 */
.sidebar-menu {
  flex: 1;
  border: none;
  background: transparent !important;
  overflow-y: auto;
  padding: 10px 0;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 100%;
}

.sidebar-menu .el-menu-item,
.sidebar-menu .el-sub-menu__title {
  height: 50px;
  line-height: 50px;
  color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 8px;
  margin: 2px 10px;
  transition: all 0.3s ease;
  background: transparent !important;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-sub-menu__title:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.sidebar-menu .el-menu-item.is-active {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  font-weight: 600;
}

.sidebar-menu .el-sub-menu .el-menu-item {
  margin: 2px 20px;
  padding-left: 40px !important;
  background: transparent !important;
}

.sidebar-menu .el-sub-menu .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.15) !important;
}

.sidebar-menu .el-sub-menu .el-menu-item.is-active {
  background: rgba(255, 255, 255, 0.25) !important;
}

.sidebar-menu .el-sub-menu .el-menu {
  background: transparent !important;
}



/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 10;
  flex: 1;
  height: 100vh;
  width: calc(100vw - 260px);
  background: #ffffff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.top-navbar {
  height: 60px;
  background: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  flex-shrink: 0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.navbar-left {
  display: flex;
  align-items: center;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.user-section {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(102, 126, 234, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.user-info:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.2);
}

.user-avatar {
  border: 2px solid #667eea;
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  color: #2c3e50;
  font-weight: 600;
  font-size: 14px;
}

.user-role {
  color: #909399;
  font-size: 12px;
}

.dropdown-icon {
  color: #909399;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}



.content-wrapper {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  padding: 0;
}

/* 侧边栏折叠时主内容区域宽度调整 */
.sidebar.collapsed + .main-content {
  width: calc(100vw - 64px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    width: 240px;
  }

  .sidebar.collapsed {
    width: 64px;
  }

  .main-content {
    width: calc(100vw - 240px);
  }

  .sidebar.collapsed + .main-content {
    width: calc(100vw - 64px);
  }

  .user-info {
    padding: 6px 12px;
  }

  .user-details {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -260px;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .sidebar.show {
    left: 0;
  }

  .main-content {
    width: 100vw;
  }

  .top-navbar {
    padding: 0 16px;
  }


}

/* Element Plus 样式覆盖 */
.sidebar-menu .el-menu-item .el-icon,
.sidebar-menu .el-sub-menu__title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9) !important;
}

.sidebar-menu.el-menu--collapse .el-menu-item .el-icon,
.sidebar-menu.el-menu--collapse .el-sub-menu__title .el-icon {
  margin-right: 0;
}

.sidebar-menu .el-sub-menu .el-menu {
  background: transparent !important;
}

.sidebar-menu .el-sub-menu__icon-arrow {
  color: rgba(255, 255, 255, 0.6) !important;
}

.sidebar-menu .el-menu-item span,
.sidebar-menu .el-sub-menu__title span {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 滚动条样式 */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.content-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.content-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }

  .logo-section {
    margin-bottom: 10px;
  }

  .header-menu {
    width: 100%;
  }

  .app-header {
    height: auto !important;
  }
}
</style>