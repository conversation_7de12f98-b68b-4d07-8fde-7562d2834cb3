<template>
  <div class="product-archive-view">
    <div class="page-header">
      <div class="header-left">
        <h2>商品档案</h2>
        <p class="page-description">管理商品基础信息档案，建立完整的商品数据库</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增商品档案
        </el-button>
        <el-button @click="exportArchive">
          <el-icon><Download /></el-icon>
          导出档案
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="商品分类">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable style="width: 150px">
            <el-option label="电子产品" value="electronics" />
            <el-option label="服装鞋帽" value="clothing" />
            <el-option label="家居用品" value="home" />
            <el-option label="美妆护肤" value="beauty" />
            <el-option label="食品饮料" value="food" />
            <el-option label="运动户外" value="sports" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="品牌">
          <el-input
            v-model="searchForm.brand"
            placeholder="请输入品牌"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="在售" value="active" />
            <el-option label="停售" value="inactive" />
            <el-option label="缺货" value="out_of_stock" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="searchProducts">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 商品档案列表 -->
    <el-card class="archive-list-card">
      <template #header>
        <div class="card-header">
          <span>商品档案列表 (共 {{ total }} 条)</span>
          <div class="header-actions">
            <el-button-group>
              <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
                列表视图
              </el-button>
              <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                <el-icon><Grid /></el-icon>
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="productArchives" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="商品信息" min-width="250">
            <template #default="{ row }">
              <div class="product-info">
                <img :src="getImageUrl(row.image)" :alt="row.name" class="product-image" />
                <div class="product-details">
                  <div class="product-name">{{ row.name }}</div>
                  <div class="product-sku">SKU: {{ row.sku }}</div>
                  <div class="product-brand">{{ row.brand }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="category" label="分类" width="120">
            <template #default="{ row }">
              <el-tag>{{ getCategoryLabel(row.category) }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="price" label="价格" width="100">
            <template #default="{ row }">
              <span class="price">¥{{ row.price.toFixed(2) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="cost" label="成本" width="100">
            <template #default="{ row }">
              <span class="cost">¥{{ row.cost.toFixed(2) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="stock" label="库存" width="80">
            <template #default="{ row }">
              <span :class="{ 'low-stock': row.stock < 10 }">{{ row.stock }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="创建时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewProduct(row)">查看</el-button>
              <el-button size="small" type="primary" @click="editProduct(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteProduct(row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="products-grid">
          <div 
            v-for="product in productArchives" 
            :key="product.id"
            class="product-card"
          >
            <div class="card-image">
              <img :src="getImageUrl(product.image)" :alt="product.name" />
              <div class="card-status">
                <el-tag :type="getStatusTagType(product.status)" size="small">
                  {{ getStatusLabel(product.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="card-content">
              <h4 class="card-title">{{ product.name }}</h4>
              <p class="card-sku">SKU: {{ product.sku }}</p>
              <p class="card-brand">{{ product.brand }}</p>
              <div class="card-category">
                <el-tag size="small">{{ getCategoryLabel(product.category) }}</el-tag>
              </div>
              
              <div class="card-price-info">
                <div class="price-row">
                  <span class="label">售价:</span>
                  <span class="price">¥{{ product.price.toFixed(2) }}</span>
                </div>
                <div class="price-row">
                  <span class="label">成本:</span>
                  <span class="cost">¥{{ product.cost.toFixed(2) }}</span>
                </div>
                <div class="price-row">
                  <span class="label">库存:</span>
                  <span :class="{ 'low-stock': product.stock < 10 }">{{ product.stock }}</span>
                </div>
              </div>
            </div>
            
            <div class="card-actions">
              <el-button size="small" @click="viewProduct(product)">查看</el-button>
              <el-button size="small" type="primary" @click="editProduct(product)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteProduct(product.id)">删除</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑商品对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingProduct ? '编辑商品档案' : '新增商品档案'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="productForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input v-model="productForm.name" placeholder="请输入商品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品SKU" prop="sku">
              <el-input v-model="productForm.sku" placeholder="请输入商品SKU" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="productForm.brand" placeholder="请输入品牌" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="productForm.category" placeholder="选择分类" style="width: 100%">
                <el-option label="电子产品" value="electronics" />
                <el-option label="服装鞋帽" value="clothing" />
                <el-option label="家居用品" value="home" />
                <el-option label="美妆护肤" value="beauty" />
                <el-option label="食品饮料" value="food" />
                <el-option label="运动户外" value="sports" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="售价" prop="price">
              <el-input-number
                v-model="productForm.price"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="成本" prop="cost">
              <el-input-number
                v-model="productForm.cost"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="库存" prop="stock">
              <el-input-number
                v-model="productForm.stock"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="productForm.status" placeholder="选择状态" style="width: 100%">
                <el-option label="在售" value="active" />
                <el-option label="停售" value="inactive" />
                <el-option label="缺货" value="out_of_stock" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品图片" prop="image">
              <el-input v-model="productForm.image" placeholder="请输入图片URL" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="商品描述">
          <el-input
            v-model="productForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入商品描述"
          />
        </el-form-item>
        
        <el-form-item label="规格参数">
          <el-input
            v-model="productForm.specifications"
            type="textarea"
            :rows="3"
            placeholder="请输入规格参数，如：尺寸、重量、材质等"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveProduct" :loading="saving">
          {{ editingProduct ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 商品详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="商品档案详情"
      width="600px"
    >
      <div v-if="selectedProduct" class="product-detail">
        <div class="detail-header">
          <img :src="getImageUrl(selectedProduct.image)" :alt="selectedProduct.name" class="detail-image" />
          <div class="detail-info">
            <h3>{{ selectedProduct.name }}</h3>
            <p><strong>SKU:</strong> {{ selectedProduct.sku }}</p>
            <p><strong>品牌:</strong> {{ selectedProduct.brand }}</p>
            <p><strong>分类:</strong> {{ getCategoryLabel(selectedProduct.category) }}</p>
            <p><strong>状态:</strong> 
              <el-tag :type="getStatusTagType(selectedProduct.status)">
                {{ getStatusLabel(selectedProduct.status) }}
              </el-tag>
            </p>
          </div>
        </div>
        
        <div class="detail-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="售价">¥{{ selectedProduct.price.toFixed(2) }}</el-descriptions-item>
            <el-descriptions-item label="成本">¥{{ selectedProduct.cost.toFixed(2) }}</el-descriptions-item>
            <el-descriptions-item label="库存">{{ selectedProduct.stock }}</el-descriptions-item>
            <el-descriptions-item label="利润率">{{ ((selectedProduct.price - selectedProduct.cost) / selectedProduct.price * 100).toFixed(1) }}%</el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">{{ formatDateTime(selectedProduct.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间" :span="2">{{ formatDateTime(selectedProduct.updated_at) }}</el-descriptions-item>
          </el-descriptions>
          
          <div class="detail-section" v-if="selectedProduct.description">
            <h4>商品描述</h4>
            <p>{{ selectedProduct.description }}</p>
          </div>
          
          <div class="detail-section" v-if="selectedProduct.specifications">
            <h4>规格参数</h4>
            <p>{{ selectedProduct.specifications }}</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  Search,
  List,
  Grid
} from '@element-plus/icons-vue'
import { productApi, type Product, type ProductQuery } from '@/api/products'

// 类型定义
type ProductArchive = Product

interface SearchForm {
  name: string
  category: string
  brand: string
  status: string
}

interface ProductForm {
  name: string
  sku?: string
  brand?: string
  category: string
  price: number
  cost: number
  stock: number
  status: string
  image?: string
  description?: string
  specifications?: string
}

// 响应式数据
const productArchives = ref<ProductArchive[]>([])
const loading = ref(false)
const saving = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const editingProduct = ref<ProductArchive | null>(null)
const selectedProduct = ref<ProductArchive | null>(null)

const searchForm = reactive<SearchForm>({
  name: '',
  category: '',
  brand: '',
  status: ''
})

const productForm = reactive<ProductForm>({
  name: '',
  sku: '',
  brand: '',
  category: '',
  price: 0,
  cost: 0,
  stock: 0,
  status: 'active',
  image: '',
  description: '',
  specifications: ''
})

const formRef = ref()

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  price: [{ required: true, message: '请输入售价', trigger: 'blur' }],
  cost: [{ required: true, message: '请输入成本', trigger: 'blur' }],
  stock: [{ required: true, message: '请输入库存', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 方法
const fetchProductArchives = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params: ProductQuery = {
      name: searchForm.name || undefined,
      category: searchForm.category || undefined,
      brand: searchForm.brand || undefined,
      status: searchForm.status || undefined,
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 调用API获取商品列表
    const response = await productApi.getProducts(params)

    productArchives.value = response.items
    total.value = response.total

  } catch (error) {
    console.error('获取商品档案失败:', error)
    ElMessage.error('获取商品档案失败')
  } finally {
    loading.value = false
  }
}

const searchProducts = () => {
  // 实现搜索逻辑
  fetchProductArchives()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    category: '',
    brand: '',
    status: ''
  })
  fetchProductArchives()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchProductArchives()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchProductArchives()
}

const viewProduct = (product: ProductArchive) => {
  selectedProduct.value = product
  showDetailDialog.value = true
}

const editProduct = (product: ProductArchive) => {
  editingProduct.value = product
  Object.assign(productForm, {
    name: product.name || '',
    sku: product.sku || '',
    brand: product.brand || '',
    category: product.category || '',
    price: product.price || 0,
    cost: product.cost || 0,
    stock: product.stock || 0,
    status: product.status || 'active',
    image: product.image || '',
    description: product.description || '',
    specifications: product.specifications || ''
  })
  showAddDialog.value = true
}

const deleteProduct = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个商品档案吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用API删除商品
    await productApi.deleteProduct(id)
    ElMessage.success('删除成功')

    // 刷新商品列表
    await fetchProductArchives()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const saveProduct = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    if (editingProduct.value) {
      // 更新现有商品
      const updateData: Partial<Product> = {
        name: productForm.name || '',
        sku: productForm.sku || undefined,
        brand: productForm.brand || undefined,
        category: productForm.category || '',
        price: productForm.price || 0,
        cost: productForm.cost || 0,
        stock: productForm.stock || 0,
        description: productForm.description || undefined,
        specifications: productForm.specifications || undefined,
        image: productForm.image || undefined,
        status: (productForm.status as 'active' | 'inactive' | 'discontinued') || 'active'
      }

      await productApi.updateProduct(editingProduct.value.id, updateData)
      ElMessage.success('更新成功')
    } else {
      // 新增商品
      const createData = {
        name: productForm.name || '',
        sku: productForm.sku || undefined,
        brand: productForm.brand || undefined,
        category: productForm.category || '',
        price: productForm.price || 0,
        cost: productForm.cost || 0,
        stock: productForm.stock || 0,
        description: productForm.description || undefined,
        specifications: productForm.specifications || undefined,
        image: productForm.image || undefined,
        status: (productForm.status as 'active' | 'inactive' | 'discontinued') || 'active',
        is_active: (productForm.status || 'active') === 'active'
      } as any

      await productApi.createProduct(createData)
      ElMessage.success('新增成功')
    }

    showAddDialog.value = false
    resetForm()

    // 刷新商品列表
    await fetchProductArchives()

  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  editingProduct.value = null
  Object.assign(productForm, {
    name: '',
    sku: '',
    brand: '',
    category: '',
    price: 0,
    cost: 0,
    stock: 0,
    status: 'active',
    image: '',
    description: '',
    specifications: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const exportArchive = () => {
  ElMessage.success('商品档案导出成功')
}

// 辅助方法
const getImageUrl = (imageUrl?: string) => {
  if (imageUrl) return imageUrl
  // 返回一个简单的SVG占位图片
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmN2ZhIiBzdHJva2U9IiNlNGU3ZWQiIHN0cm9rZS13aWR0aD0iMiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE2IiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTA5Mzk5Ij7llYblk4Hlm77niYc8L3RleHQ+PC9zdmc+'
}

const getCategoryLabel = (category: string) => {
  const labelMap: Record<string, string> = {
    'electronics': '电子产品',
    'clothing': '服装鞋帽',
    'home': '家居用品',
    'beauty': '美妆护肤',
    'food': '食品饮料',
    'sports': '运动户外'
  }
  return labelMap[category] || category
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'active': '在售',
    'inactive': '停售',
    'out_of_stock': '缺货'
  }
  return labelMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'active': 'success',
    'inactive': 'info',
    'out_of_stock': 'warning'
  }
  return typeMap[status] || 'info'
}

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string | undefined) => {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchProductArchives()
})
</script>

<style scoped>
.product-archive-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 档案列表卡片 */
.archive-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 商品信息 */
.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid #e4e7ed;
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.product-sku {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.product-brand {
  font-size: 12px;
  color: #606266;
}

/* 价格样式 */
.price {
  font-weight: 600;
  color: #E6A23C;
}

.cost {
  font-weight: 600;
  color: #909399;
}

.low-stock {
  color: #F56C6C;
  font-weight: 600;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-status {
  position: absolute;
  top: 10px;
  right: 10px;
}

.card-content {
  padding: 16px;
}

.card-title {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.card-sku,
.card-brand {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #909399;
}

.card-category {
  margin: 8px 0;
}

.card-price-info {
  margin-top: 12px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 14px;
}

.price-row .label {
  color: #606266;
}

.card-actions {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 商品详情 */
.product-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid #e4e7ed;
}

.detail-info h3 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 20px;
}

.detail-info p {
  margin: 0 0 8px 0;
  color: #606266;
}

.detail-content {
  margin-top: 20px;
}

.detail-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
}

.detail-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

/* 按钮样式 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .product-archive-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-info {
    flex-direction: column;
    text-align: center;
  }

  .detail-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>
