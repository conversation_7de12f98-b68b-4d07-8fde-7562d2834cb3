"""
库存数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Boolean, Float, ForeignKey, Numeric, Enum, Date
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class Inventory(Base):
    """库存模型"""
    __tablename__ = "inventory"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False, comment="仓库ID")
    
    # 库存数量
    current_stock = Column(Integer, default=0, comment="当前库存")
    reserved_stock = Column(Integer, default=0, comment="预留库存")
    available_stock = Column(Integer, default=0, comment="可用库存")
    
    # 库存限制
    min_stock = Column(Integer, default=0, comment="最小库存")
    max_stock = Column(Integer, default=0, comment="最大库存")
    safety_stock = Column(Integer, default=0, comment="安全库存")
    
    # 成本信息
    average_cost = Column(Float, default=0.0, comment="平均成本")
    last_cost = Column(Float, default=0.0, comment="最新成本")
    
    # 位置信息
    location = Column(String(100), comment="库位")
    zone = Column(String(50), comment="库区")

    # 批次信息
    batch_no = Column(String(100), comment="批次号")
    production_date = Column(Date, comment="生产日期")
    expiry_date = Column(Date, comment="到期日期")

    # 状态
    status = Column(String(20), default="normal", comment="状态: normal, locked, damaged")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    last_in_date = Column(DateTime(timezone=True), comment="最后入库时间")
    last_out_date = Column(DateTime(timezone=True), comment="最后出库时间")
    
    # 关联关系
    product = relationship("Product", back_populates="inventory_items")
    warehouse = relationship("Warehouse", back_populates="inventory_items")

    def __repr__(self):
        return f"<Inventory(id={self.id}, product_id={self.product_id}, warehouse_id={self.warehouse_id}, stock={self.current_stock})>"


class InventoryTransaction(Base):
    """库存变动记录"""
    __tablename__ = "inventory_transactions"

    id = Column(Integer, primary_key=True, index=True)
    inventory_id = Column(Integer, ForeignKey("inventory.id"), nullable=False, comment="库存ID")
    
    # 变动信息
    transaction_type = Column(String(20), nullable=False, comment="变动类型: in, out, adjust, transfer")
    quantity = Column(Integer, nullable=False, comment="变动数量")
    before_quantity = Column(Integer, nullable=False, comment="变动前数量")
    after_quantity = Column(Integer, nullable=False, comment="变动后数量")
    
    # 关联单据
    reference_type = Column(String(50), comment="关联单据类型")
    reference_id = Column(Integer, comment="关联单据ID")
    reference_no = Column(String(100), comment="关联单据号")


    
    # 操作信息
    operator = Column(String(100), comment="操作人")
    reason = Column(String(200), comment="变动原因")
    remark = Column(Text, comment="备注")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关联关系
    inventory = relationship("Inventory")


    def __repr__(self):
        return f"<InventoryTransaction(id={self.id}, type='{self.transaction_type}', quantity={self.quantity})>"


class InventoryTransferStatus(enum.Enum):
    """库存调拨单状态"""
    DRAFT = "draft"           # 草稿
    SUBMITTED = "submitted"   # 已提交
    APPROVED = "approved"     # 已审核
    REJECTED = "rejected"     # 已拒绝
    TRANSFERRED = "transferred"  # 已调拨
    COMPLETED = "completed"   # 已完成
    CANCELLED = "cancelled"   # 已取消


class InventoryTransfer(Base):
    """库存调拨单模型"""
    __tablename__ = "inventory_transfers"

    id = Column(Integer, primary_key=True, index=True)
    transfer_no = Column(String(50), unique=True, index=True, nullable=False, comment="调拨单号")

    # 仓库信息
    from_warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False, comment="调出仓库ID")
    to_warehouse_id = Column(Integer, ForeignKey("warehouses.id"), nullable=False, comment="调入仓库ID")

    # 基本信息
    transfer_date = Column(DateTime, nullable=False, comment="调拨日期")
    reason = Column(Text, nullable=False, comment="调拨原因")
    total_quantity = Column(Integer, nullable=False, default=0, comment="调拨总数量")

    # 状态信息
    status = Column(Enum(InventoryTransferStatus), default=InventoryTransferStatus.DRAFT, comment="调拨单状态")

    # 审核信息
    submitted_at = Column(DateTime, nullable=True, comment="提交时间")
    submitted_by = Column(String(50), nullable=True, comment="提交人")
    approved_at = Column(DateTime, nullable=True, comment="审核时间")
    approved_by = Column(String(50), nullable=True, comment="审核人")
    approval_note = Column(Text, nullable=True, comment="审核备注")

    # 调拨信息
    transferred_at = Column(DateTime, nullable=True, comment="调拨时间")
    transferred_by = Column(String(50), nullable=True, comment="调拨人")

    # 备注信息
    remark = Column(Text, nullable=True, comment="备注")

    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(String(50), nullable=True, comment="创建人")
    updated_by = Column(String(50), nullable=True, comment="更新人")

    # 关系
    from_warehouse = relationship("Warehouse", foreign_keys=[from_warehouse_id])
    to_warehouse = relationship("Warehouse", foreign_keys=[to_warehouse_id])
    items = relationship("InventoryTransferItem", back_populates="inventory_transfer", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<InventoryTransfer(id={self.id}, transfer_no='{self.transfer_no}', status='{self.status}')>"


class InventoryTransferItem(Base):
    """库存调拨单明细模型"""
    __tablename__ = "inventory_transfer_items"

    id = Column(Integer, primary_key=True, index=True)
    inventory_transfer_id = Column(Integer, ForeignKey("inventory_transfers.id"), nullable=False, comment="库存调拨单ID")

    # 商品信息
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_sku = Column(String(100), nullable=True, comment="商品SKU")

    # 调拨信息
    transfer_quantity = Column(Integer, nullable=False, comment="调拨数量")
    available_quantity = Column(Integer, nullable=True, comment="可用库存数量")

    # 批次信息
    batch_no = Column(String(100), nullable=True, comment="批次号")



    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    inventory_transfer = relationship("InventoryTransfer", back_populates="items")
    product = relationship("Product")


    def __repr__(self):
        return f"<InventoryTransferItem(id={self.id}, product_name='{self.product_name}', transfer_quantity={self.transfer_quantity})>"












