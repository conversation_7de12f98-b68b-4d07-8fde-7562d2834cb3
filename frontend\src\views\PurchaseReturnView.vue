<template>
  <div class="purchase-return-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>采购退货单</h2>
        <p class="page-description">管理采购退货单，跟踪退货流程和质量问题</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建退货单
        </el-button>
        <el-button @click="exportReturns">
          <el-icon><Download /></el-icon>
          导出退货单
        </el-button>
      </div>
    </div>

    <!-- 退货单概览 -->
    <el-row :gutter="20" class="return-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon total">
              <el-icon size="32"><Tickets /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.total_returns }}</div>
              <div class="overview-label">退货单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon pending">
              <el-icon size="32"><Clock /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.draft_returns }}</div>
              <div class="overview-label">草稿</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon processing">
              <el-icon size="32"><Loading /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.submitted_returns }}</div>
              <div class="overview-label">已提交</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon completed">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-value">{{ returnStats.approved_returns }}</div>
              <div class="overview-label">已审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="退货单号">
          <el-input
            v-model="searchForm.return_no"
            placeholder="请输入退货单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="供应商">
          <el-select v-model="searchForm.supplier_id" placeholder="选择供应商" clearable style="width: 200px">
            <el-option
              v-for="supplier in suppliers"
              :key="supplier.id"
              :label="supplier.name"
              :value="supplier.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="单据状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已退货" value="returned" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>

        <el-form-item label="退货日期">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="fetchPurchaseReturns" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 退货单列表 -->
    <el-card class="return-list-card">
      <template #header>
        <div class="card-header">
          <span>退货单列表</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''" 
                @click="viewMode = 'table'"
                :icon="List"
              >
                表格视图
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''" 
                @click="viewMode = 'card'"
                :icon="Grid"
              >
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="purchaseReturns" style="width: 100%" v-loading="loading">
          <el-table-column prop="return_no" label="退货单号" width="180" />
          <el-table-column prop="receipt_no" label="入库单号" width="150">
            <template #default="{ row }">
              <span v-if="row.receipt_no">{{ row.receipt_no }}</span>
              <span v-else class="text-gray">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="supplier_name" label="供应商" width="150" />
          <el-table-column prop="total_amount" label="退货金额" width="120">
            <template #default="{ row }">
              <span class="amount">¥{{ parseFloat(String(row.total_amount || 0)).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="return_date" label="退货日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.return_date) }}
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="退货原因" min-width="200" show-overflow-tooltip />
          <el-table-column prop="created_at" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="viewReturnDetail(row)">查看</el-button>
                <el-button
                  size="small"
                  type="primary"
                  @click="handleReturnAction('submit', row)"
                  v-if="row.status === 'draft'"
                >
                  提交
                </el-button>
                <el-button
                  size="small"
                  type="warning"
                  @click="handleReturnAction('withdraw', row)"
                  v-if="row.status === 'submitted'"
                >
                  撤销
                </el-button>
                <el-dropdown @command="(command: string) => handleReturnAction(command, row)" v-if="row.status !== 'returned' && row.status !== 'rejected'">
                  <el-button size="small" type="primary">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit" v-if="row.status === 'draft'">编辑</el-dropdown-item>
                      <el-dropdown-item command="approve" v-if="row.status === 'submitted'">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="row.status === 'submitted'">审核拒绝</el-dropdown-item>
                      <el-dropdown-item command="return" v-if="row.status === 'approved'">确认退货</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="row.status === 'draft'" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col :span="8" v-for="returnItem in purchaseReturns" :key="returnItem.id">
            <el-card class="return-card" @click="viewReturnDetail(returnItem)">
              <div class="card-header">
                <h3>{{ returnItem.return_no }}</h3>
                <el-tag :type="getStatusTagType(returnItem.status)">
                  {{ getStatusLabel(returnItem.status) }}
                </el-tag>
              </div>
              <div class="card-content">
                <p><strong>入库单号:</strong>
                  <span v-if="returnItem.receipt_no">{{ returnItem.receipt_no }}</span>
                  <span v-else class="text-gray">-</span>
                </p>
                <p><strong>供应商:</strong> {{ returnItem.supplier_name }}</p>
                <p><strong>退货金额:</strong> <span class="amount">¥{{ parseFloat(String(returnItem.total_amount || 0)).toFixed(2) }}</span></p>
                <p><strong>退货日期:</strong> {{ formatDate(returnItem.return_date) }}</p>
                <p><strong>退货原因:</strong> {{ returnItem.reason }}</p>
              </div>
              <div class="card-actions" @click.stop>
                <el-button size="small" @click="viewReturnDetail(returnItem)">查看详情</el-button>
                <el-button
                  size="small"
                  type="primary"
                  @click="handleReturnAction('submit', returnItem)"
                  v-if="returnItem.status === 'draft'"
                >
                  提交
                </el-button>
                <el-button
                  size="small"
                  type="warning"
                  @click="handleReturnAction('withdraw', returnItem)"
                  v-if="returnItem.status === 'submitted'"
                >
                  撤销
                </el-button>
                <el-dropdown @command="(command: string) => handleReturnAction(command, returnItem)" v-if="returnItem.status !== 'returned' && returnItem.status !== 'rejected'">
                  <el-button size="small" type="primary">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit" v-if="returnItem.status === 'draft'">编辑</el-dropdown-item>
                      <el-dropdown-item command="approve" v-if="returnItem.status === 'submitted'">审核通过</el-dropdown-item>
                      <el-dropdown-item command="reject" v-if="returnItem.status === 'submitted'">审核拒绝</el-dropdown-item>
                      <el-dropdown-item command="return" v-if="returnItem.status === 'approved'">确认退货</el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="returnItem.status === 'draft'" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchPurchaseReturns"
          @current-change="fetchPurchaseReturns"
        />
      </div>
    </el-card>

    <!-- 创建/编辑退货单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEditing ? '编辑退货单' : '新建退货单'"
      width="1000px"
      :close-on-click-modal="false"
      @close="resetReturnForm"
    >
      <el-form
        ref="returnFormRef"
        :model="returnForm"
        :rules="returnFormRules"
        label-width="100px"
      >
        <!-- 退货方式选择 -->
        <el-form-item label="退货方式">
          <el-radio-group v-model="returnForm.return_type" @change="onReturnTypeChange">
            <el-radio value="manual">手动创建</el-radio>
            <el-radio value="from_receipt">从收货单创建</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 选择收货单 -->
        <el-form-item
          v-if="returnForm.return_type === 'from_receipt'"
          label="收货单"
          prop="receipt_no"
        >
          <el-select
            v-model="returnForm.receipt_no"
            placeholder="选择收货单"
            style="width: 100%"
            filterable
            @change="onReceiptChange"
            :loading="loadingReceipts"
          >
            <el-option
              v-for="receipt in availableReceipts"
              :key="receipt.id"
              :label="`${receipt.receipt_no} - ${receipt.supplier_name} (${formatDate(receipt.received_date)})`"
              :value="receipt.receipt_no"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier_id">
              <el-select
                v-model="returnForm.supplier_id"
                placeholder="选择供应商"
                style="width: 100%"
                :disabled="returnForm.return_type === 'from_receipt'"
                filterable
              >
                <el-option
                  v-for="supplier in suppliers"
                  :key="supplier.id"
                  :label="supplier.name"
                  :value="supplier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退货日期" prop="return_date">
              <el-date-picker
                v-model="returnForm.return_date"
                type="date"
                placeholder="选择退货日期"
                style="width: 100%"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              v-if="returnForm.return_type === 'from_receipt'"
              label="收货单号"
            >
              <el-input
                v-model="returnForm.receipt_no"
                placeholder="由选择的收货单自动填充"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退货单号">
              <el-input
                v-model="returnForm.return_no"
                placeholder="系统自动生成"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="退货原因" prop="reason">
          <el-input
            v-model="returnForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入退货原因"
          />
        </el-form-item>

        <el-form-item label="备注信息">
          <el-input
            v-model="returnForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 退货商品列表 -->
        <el-form-item label="退货商品" required>
          <div class="return-items">
            <div class="items-header">
              <el-button
                size="small"
                @click="addReturnItem"
                v-if="returnForm.return_type === 'manual'"
              >
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
              <div v-if="returnForm.return_type === 'from_receipt'" class="receipt-mode-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>从收货单创建模式：商品列表由所选收货单自动生成，不可手动添加</span>
              </div>
            </div>

            <div class="table-wrapper">
              <div class="custom-table">
                <!-- 表格头部 -->
                <div class="table-header">
                  <div class="header-cell product-name">商品名称</div>
                  <div class="header-cell sku">SKU</div>
                  <div class="header-cell batch-no-wide">批次号</div>
                  <div class="header-cell warehouse">退货仓库</div>
                  <div class="header-cell quantity">退货数量</div>
                  <div class="header-cell unit-price">单价</div>
                  <div class="header-cell total-price">小计</div>
                  <div class="header-cell quality-issue">质量问题</div>
                  <div class="header-cell actions">操作</div>
                </div>

                <!-- 表格内容 -->
                <div class="table-body">
                  <div v-for="(row, index) in returnForm.items" :key="index" class="table-row">
                    <div class="table-cell product-name">
                      <el-select
                        v-model="row.product_id"
                        placeholder="选择商品"
                        filterable
                        @change="onProductChange(row)"
                        :disabled="returnForm.return_type === 'from_receipt'"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="product in availableProducts"
                          :key="product.id"
                          :label="product.name"
                          :value="product.id"
                        />
                      </el-select>
                    </div>

                    <div class="table-cell sku">
                      <span>{{ row.product_sku || '-' }}</span>
                    </div>

                    <div class="table-cell batch-no-wide">
                      <!-- 统一使用下拉选择批次 -->
                      <el-select
                        v-model="row.batch_no"
                        placeholder="选择批次"
                        size="small"
                        style="width: 100%"
                        :disabled="!row.product_id"
                        @change="onBatchChange(row)"
                        clearable
                      >
                        <el-option
                          v-for="batch in row.available_batches || []"
                          :key="batch.batch_no"
                          :label="`${batch.batch_no} (可退:${batch.returnable_quantity})`"
                          :value="batch.batch_no"
                        />
                      </el-select>
                    </div>

                    <div class="table-cell warehouse">
                      <el-select
                        v-model="row.warehouse_id"
                        placeholder="选择仓库"
                        style="width: 100%"
                        size="small"
                        :disabled="!row.product_id || !row.batch_no"
                        @change="onWarehouseChange(row)"
                      >
                        <el-option
                          v-for="warehouse in getFilteredWarehouses(row)"
                          :key="warehouse.id"
                          :label="warehouse.display_name"
                          :value="warehouse.id"
                        />
                      </el-select>
                    </div>

                    <div class="table-cell quantity">
                      <el-input-number
                        v-model="row.return_quantity"
                        :min="0"
                        :max="returnForm.return_type === 'from_receipt' ? row.max_quantity : undefined"
                        style="width: 100%"
                        size="small"
                        :placeholder="returnForm.return_type === 'from_receipt' ? `最多${row.max_quantity || 0}` : '请输入数量'"
                        @change="calculateItemTotal(row)"
                      />
                    </div>

                    <div class="table-cell unit-price">
                      <el-input-number
                        v-model="row.unit_price"
                        :min="0"
                        :precision="2"
                        style="width: 100%"
                        size="small"
                        placeholder="请输入单价"
                        @change="calculateItemTotal(row)"
                      />
                    </div>

                    <div class="table-cell total-price">
                      <span>¥{{ (Number(row.total_price) || 0).toFixed(2) }}</span>
                    </div>

                    <div class="table-cell quality-issue">
                      <el-input
                        v-model="row.quality_issue"
                        placeholder="描述质量问题"
                        style="width: 100%"
                        size="small"
                      />
                    </div>

                    <div class="table-cell actions">
                      <el-button
                        type="danger"
                        size="small"
                        @click="removeReturnItem(index)"
                        :disabled="isEditing && returnForm.return_type === 'from_receipt'"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div style="margin-top: 10px; text-align: right">
              <strong>总金额: ¥{{ (Number(returnForm.total_amount) || 0).toFixed(2) }}</strong>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveReturn" :loading="saving">
            {{ isEditing ? '更新' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退货单详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="退货单详情"
      width="800px"
    >
      <div v-if="selectedReturn">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="退货单号">{{ selectedReturn.return_no }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ selectedReturn.supplier_name }}</el-descriptions-item>
          <el-descriptions-item label="入库单号">{{ selectedReturn.receipt_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="退货日期">{{ formatDate(selectedReturn.return_date) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedReturn.status)">
              {{ getStatusLabel(selectedReturn.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="退货金额">¥{{ parseFloat(String(selectedReturn.total_amount || 0)).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="退货原因" :span="2">{{ selectedReturn.reason }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ selectedReturn.remark || '-' }}</el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px">
          <h4>退货明细</h4>
          <el-table :data="selectedReturn.items" border>
            <el-table-column label="商品信息" width="200">
              <template #default="{ row }">
                <div>
                  <div style="font-weight: 500; margin-bottom: 4px;">{{ row.product_name }}</div>
                  <div style="font-size: 12px; color: #909399;">SKU: {{ row.product_sku || '-' }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="batch_no" label="批次号" width="120">
              <template #default="{ row }">
                {{ row.batch_no || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="warehouse_name" label="退货仓库" width="120" />
            <el-table-column prop="return_quantity" label="退货数量" width="100" />
            <el-table-column label="单价" width="100">
              <template #default="{ row }">
                ¥{{ parseFloat(String(row.unit_price || 0)).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="小计" width="100">
              <template #default="{ row }">
                ¥{{ parseFloat(String(row.total_price || 0)).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="quality_issue" label="质量问题" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  purchaseReturnApi,
  purchaseApi,
  type PurchaseReturn,
  type PurchaseReturnStats,
  type PurchaseOrder,
  PurchaseReturnStatus,
  PurchaseOrderStatus
} from '@/api/purchase'
import { supplierApi, type Supplier } from '@/api/suppliers'
import { productApi, type Product } from '@/api/products'
import { warehouseApi } from '@/api/warehouses'
import {
  Plus,
  Download,
  Tickets,
  Clock,
  Loading,
  CircleCheck,
  Search,
  List,
  Grid,
  ArrowDown,
  Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const purchaseReturns = ref<PurchaseReturn[]>([])
const suppliers = ref<Supplier[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const viewMode = ref<'table' | 'card'>('table')

const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedReturn = ref<PurchaseReturn | null>(null)
const isEditing = ref(false)
const saving = ref(false)
const returnFormRef = ref()
const loadingReceipts = ref(false)

// 可用的收货单和商品
const availableReceipts = ref<any[]>([])
const availableProducts = ref<any[]>([])
const warehouses = ref<any[]>([])
// 每个商品对应的仓库列表
const productWarehouses = ref<{ [productId: number]: any[] }>({})

// 退货单表单
const returnForm = reactive({
  id: null as number | null,
  return_type: 'manual' as 'manual' | 'from_receipt',
  return_no: '',
  supplier_id: null as number | null,
  receipt_no: '',
  return_date: '',
  reason: '',
  remark: '',
  total_amount: 0,
  status: 'draft' as string,
  items: [] as any[]
})

// 表单验证规则
const returnFormRules = {
  receipt_no: [
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (returnForm.return_type === 'from_receipt' && !value) {
          callback(new Error('请选择收货单'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  supplier_id: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  return_date: [
    { required: true, message: '请选择退货日期', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入退货原因', trigger: 'blur' }
  ]
}

// 搜索表单
const searchForm = reactive({
  return_no: '',
  supplier_id: null as number | null,
  status: '',
  date_range: null as [Date, Date] | null
})

// 统计数据
const returnStats = ref<PurchaseReturnStats>({
  total_returns: 0,
  draft_returns: 0,
  submitted_returns: 0,
  approved_returns: 0,
  returned_returns: 0,
  completed_returns: 0,
  total_amount: 0
})

// 方法
const fetchPurchaseReturns = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    if (searchForm.return_no) {
      params.return_no = searchForm.return_no
    }
    if (searchForm.supplier_id) {
      params.supplier_id = searchForm.supplier_id
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.date_range) {
      params.start_date = searchForm.date_range[0].toISOString()
      params.end_date = searchForm.date_range[1].toISOString()
    }

    const response = await purchaseReturnApi.getPurchaseReturns(params)

    if (response) {
      purchaseReturns.value = response || []
      total.value = response.length || 0
    } else {
      purchaseReturns.value = []
      total.value = 0
    }

    // 获取统计数据
    const statsResponse = await purchaseReturnApi.getStats()
    if (statsResponse) {
      returnStats.value = statsResponse
    }
  } catch (error: any) {
    console.error('获取采购退货单失败:', error)
    ElMessage.error('获取采购退货单失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

const fetchSuppliers = async () => {
  try {
    const response = await supplierApi.getSuppliers()
    if (response && (response as any).items) {
      suppliers.value = (response as any).items
    }
  } catch (error: any) {
    console.error('获取供应商列表失败:', error)
  }
}

const fetchWarehouses = async () => {
  try {
    const response = await warehouseApi.getWarehouses({ is_active: true })
    warehouses.value = (response || []).filter((w: any) => w.is_active)
  } catch (error: any) {
    console.error('获取仓库列表失败:', error)
  }
}

// 获取有指定产品库存的仓库列表
const fetchWarehousesForProduct = async (productId: number) => {
  if (!productId) return []

  try {
    const response = await purchaseReturnApi.getWarehousesWithProduct(productId)
    productWarehouses.value[productId] = response || []
    return response || []
  } catch (error: any) {
    console.error('获取产品仓库列表失败:', error)
    return []
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchPurchaseReturns()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    return_no: '',
    supplier_id: null,
    status: '',
    date_range: null
  })
  handleSearch()
}

const viewReturnDetail = (returnItem: PurchaseReturn) => {
  selectedReturn.value = returnItem
  showDetailDialog.value = true
}

const handleReturnAction = async (command: string, returnItem: PurchaseReturn) => {
  switch (command) {
    case 'edit':
      await editReturn(returnItem)
      break
    case 'submit':
      await submitReturn(returnItem)
      break
    case 'withdraw':
      await withdrawReturn(returnItem)
      break
    case 'approve':
      await approveReturn(returnItem)
      break
    case 'reject':
      await rejectReturn(returnItem)
      break
    case 'return':
      await confirmReturn(returnItem)
      break
    case 'delete':
      await deleteReturn(returnItem)
      break
    default:
      ElMessage.warning('未知操作')
  }
}

const submitReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要提交这个退货单吗？', '确认提交', {
      type: 'warning'
    })

    await purchaseReturnApi.submitPurchaseReturn(returnItem.id!)
    ElMessage.success('退货单提交成功')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交退货单失败:', error)
      ElMessage.error('提交失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const withdrawReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要撤销这个退货单吗？撤销后退货单将回到草稿状态。', '确认撤销', {
      type: 'warning'
    })

    await purchaseReturnApi.withdrawPurchaseReturn(returnItem.id!)
    ElMessage.success('退货单撤销成功')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('撤销退货单失败:', error)
      ElMessage.error('撤销失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const approveReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要审核通过这个退货单吗？', '确认审核', {
      type: 'warning'
    })

    await purchaseReturnApi.approvePurchaseReturn(returnItem.id!, {
      status: PurchaseReturnStatus.APPROVED
    })
    ElMessage.success('退货单审核通过')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核退货单失败:', error)
      ElMessage.error('审核失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const rejectReturn = async (returnItem: PurchaseReturn) => {
  try {
    const { value: rejectReason } = await ElMessageBox.prompt('请输入拒绝原因', '审核拒绝', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '拒绝原因不能为空'
    })

    await purchaseReturnApi.approvePurchaseReturn(returnItem.id!, {
      status: PurchaseReturnStatus.REJECTED,
      note: rejectReason
    })
    ElMessage.success('退货单审核拒绝')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('拒绝退货单失败:', error)
      ElMessage.error('拒绝失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const confirmReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要确认退货吗？', '确认退货', {
      type: 'warning'
    })

    await purchaseReturnApi.returnPurchaseReturn(returnItem.id!)
    ElMessage.success('退货确认成功')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('确认退货失败:', error)
      ElMessage.error('确认退货失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}



const deleteReturn = async (returnItem: PurchaseReturn) => {
  try {
    await ElMessageBox.confirm('确定要删除这个退货单吗？删除后无法恢复！', '确认删除', {
      type: 'warning'
    })

    await purchaseReturnApi.deletePurchaseReturn(returnItem.id!)
    ElMessage.success('退货单删除成功')
    fetchPurchaseReturns()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除退货单失败:', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

const exportReturns = () => {
  ElMessage.success('退货单导出成功')
}

// 退货单表单相关方法
const onReturnTypeChange = (type: 'manual' | 'from_receipt') => {
  if (type === 'from_receipt') {
    // 切换到从收货单创建模式，获取可用收货单
    fetchAvailableReceipts()
    // 清空手动输入的数据
    returnForm.supplier_id = null
    returnForm.items = []
  } else {
    // 切换到手动创建模式，清空收货单相关数据
    returnForm.receipt_no = ''
    returnForm.supplier_id = null
    returnForm.items = []
  }
}

// 选择收货单时的处理
const onReceiptChange = async (receiptNo: string) => {
  if (!receiptNo) return

  try {
    const receipt = availableReceipts.value.find(r => r.receipt_no === receiptNo)
    if (!receipt) return

    // 自动填充供应商信息和收货单信息
    returnForm.supplier_id = receipt.supplier_id
    returnForm.receipt_no = receipt.receipt_no

    // 获取该收货单的商品明细信息
    const receiptItems = await purchaseReturnApi.getReceiptItems(receipt.id)

    // 自动填充商品明细（基于收货单明细）
    returnForm.items = receiptItems.map((item: any, index: number) => ({
      product_id: item.product_id,
      product_name: item.product_name,
      product_sku: item.product_sku,
      batch_no: item.batch_no || '',
      available_batches: [], // 可用批次列表
      warehouse_id: item.warehouse_id,
      return_quantity: 0,
      unit_price: Number(item.unit_price) || 0.00,
      total_price: 0.00,
      quality_issue: '',
      max_quantity: item.quantity, // 最大可退货数量等于收货数量
      line_number: item.line_number || (index + 1),
      received_quantity: item.quantity
    })) || []

    // 为每个商品获取批次信息
    for (const item of returnForm.items) {
      if (item.product_id) {
        await fetchWarehousesForProduct(item.product_id)
        // 获取该商品的批次信息（基于收货单）
        try {
          const batches = await purchaseReturnApi.getProductBatchesFromReceipt(receipt.id, item.product_id)
          item.available_batches = batches
        } catch (error) {
          console.error('获取商品批次失败:', error)
        }
      }
    }

    calculateTotalAmount()
    ElMessage.success(`已导入 ${returnForm.items.length} 个商品`)

  } catch (error) {
    console.error('获取收货单明细失败:', error)
    ElMessage.error('获取收货单明细失败')
  }
}

const addReturnItem = () => {
  returnForm.items.push({
    product_id: null,
    product_name: '',
    product_sku: '',
    batch_no: '',
    available_batches: [], // 可用批次列表
    warehouse_id: null,
    return_quantity: 0,
    unit_price: 0.00,
    total_price: 0.00,
    quality_issue: '',
    max_quantity: 0
  })
}

const removeReturnItem = (index: number) => {
  returnForm.items.splice(index, 1)
  calculateTotalAmount()
}

// 编辑退货单
const editReturn = async (returnItem: PurchaseReturn) => {
  try {
    loading.value = true

    // 获取退货单详情
    const detail = await purchaseReturnApi.getPurchaseReturn(returnItem.id!)

    // 填充表单数据
    returnForm.id = detail.id || null
    returnForm.return_no = detail.return_no
    returnForm.receipt_no = detail.receipt_no || ''
    returnForm.supplier_id = detail.supplier_id
    returnForm.return_date = detail.return_date
    returnForm.reason = detail.reason
    returnForm.total_amount = detail.total_amount
    returnForm.remark = detail.remark || ''
    returnForm.status = detail.status

    // 设置退货方式
    returnForm.return_type = detail.receipt_no ? 'from_receipt' : 'manual'

    // 填充明细数据
    returnForm.items = detail.items?.map((item: any) => ({
      id: item.id,
      product_id: item.product_id,
      product_name: item.product_name,
      product_sku: item.product_sku,
      batch_no: item.batch_no || '',
      warehouse_id: item.warehouse_id,
      line_number: item.receipt_line_number || null, // 收货单行号
      return_quantity: Number(item.return_quantity) || 0,
      unit_price: Number(item.unit_price) || 0.00,
      total_price: Number(item.total_price) || 0.00,
      quality_issue: item.quality_issue || '',
      max_quantity: item.return_quantity // 编辑时设置为当前数量
    })) || []

    // 如果是从收货单创建的，获取可用收货单列表
    if (returnForm.return_type === 'from_receipt' && returnForm.receipt_no) {
      await fetchAvailableReceipts()

      // 编辑时，max_quantity 已经在上面设置为当前退货数量
      // 这里不需要额外处理
    }

    // 获取每个商品的仓库列表
    for (const item of returnForm.items) {
      if (item.product_id) {
        await fetchWarehousesForProduct(item.product_id)
      }
    }

    // 设置编辑状态并显示对话框
    isEditing.value = true
    showCreateDialog.value = true

  } catch (error) {
    console.error('获取退货单详情失败:', error)
    ElMessage.error('获取退货单详情失败')
  } finally {
    loading.value = false
  }
}

const onProductChange = async (item: any) => {
  const product = availableProducts.value.find(p => p.id === item.product_id)
  if (product) {
    item.product_name = product.name
    item.product_sku = product.sku

    // 获取该产品的仓库列表
    await fetchWarehousesForProduct(item.product_id)

    // 获取该商品的批次信息
    try {
      let batches = []

      if (returnForm.return_type === 'from_receipt' && returnForm.receipt_no) {
        // 从收货单退货：通过收货单号找到收货单ID，然后获取批次
        const receipt = availableReceipts.value.find(r => r.receipt_no === returnForm.receipt_no)
        if (receipt) {
          batches = await purchaseReturnApi.getProductBatchesFromReceipt(receipt.id, item.product_id)
        } else {
          // 如果找不到收货单，使用手动模式
          batches = await purchaseReturnApi.getProductAllBatches(item.product_id)
        }
      } else {
        // 手动退货：获取该商品的所有批次（基于库存）
        batches = await purchaseReturnApi.getProductAllBatches(item.product_id)
      }

      item.available_batches = batches

      // 重置批次和相关字段
      item.batch_no = ''
      item.warehouse_id = null
      item.return_quantity = 0
      item.max_quantity = 0
      item.unit_price = 0.00
      item.total_price = 0.00

    } catch (error) {
      console.error('获取商品批次失败:', error)
      ElMessage.error('获取商品批次信息失败')
      item.available_batches = []
    }
  }
}

// 批次选择变更
const onBatchChange = (item: any) => {
  if (item.batch_no && item.available_batches) {
    const selectedBatch = item.available_batches.find((batch: any) => batch.batch_no === item.batch_no)
    if (selectedBatch) {
      // 自动设置仓库
      item.warehouse_id = selectedBatch.warehouse_id

      // 设置最大可退货数量
      if (returnForm.return_type === 'from_receipt') {
        // 从收货单创建：最大可退货数量保持为收货数量，不改变
        // item.max_quantity 已经在 onReceiptChange 中设置为收货数量
      } else {
        // 手动创建：使用批次的可退货数量（基于库存）
        item.max_quantity = selectedBatch.returnable_quantity
      }

      // 设置单价
      item.unit_price = Number(selectedBatch.unit_price) || 0.00

      // 重置退货数量
      item.return_quantity = 0
      item.total_price = 0.00

      // 立即获取批次库存信息（用于仓库选择显示）
      if (item.product_id && selectedBatch.warehouse_id) {
        fetchBatchStockInfo(item.product_id, item.batch_no, selectedBatch.warehouse_id)
      }

      // 显示批次信息
      const maxQty = returnForm.return_type === 'from_receipt' ? item.max_quantity : selectedBatch.returnable_quantity
      ElMessage.success(`已选择批次 ${selectedBatch.batch_no}，可退货数量：${maxQty}`)
    }
  }
}

const calculateItemTotal = (item: any) => {
  const quantity = Number(item.return_quantity) || 0
  const price = Number(item.unit_price) || 0
  item.total_price = quantity * price
  calculateTotalAmount()
}

const calculateTotalAmount = () => {
  returnForm.total_amount = returnForm.items.reduce((sum, item) => {
    return sum + (Number(item.total_price) || 0)
  }, 0)
}

// 批次库存信息缓存
const batchStockCache = ref<Record<string, any>>({})

// 获取筛选后的仓库列表（只显示有该商品和该批次的仓库）
const getFilteredWarehouses = (item: any) => {
  if (!item.product_id || !item.batch_no) {
    return []
  }

  // 如果是从收货单创建，使用批次信息中的仓库
  if (returnForm.return_type === 'from_receipt' && item.available_batches) {
    const selectedBatch = item.available_batches.find((batch: any) => batch.batch_no === item.batch_no)
    if (selectedBatch && selectedBatch.warehouse_id) {
      // 检查缓存中是否有该批次的库存信息
      const cacheKey = `${item.product_id}_${item.batch_no}_${selectedBatch.warehouse_id}`
      if (batchStockCache.value[cacheKey]) {
        const stockInfo = batchStockCache.value[cacheKey]
        return [{
          id: selectedBatch.warehouse_id,
          name: selectedBatch.warehouse_name,
          available_stock: stockInfo.available_stock || 0,
          display_name: `${selectedBatch.warehouse_name} (库存: ${stockInfo.available_stock || 0})`
        }]
      }

      // 如果缓存中没有，异步获取库存信息并返回临时信息
      fetchBatchStockInfo(item.product_id, item.batch_no, selectedBatch.warehouse_id)

      return [{
        id: selectedBatch.warehouse_id,
        name: selectedBatch.warehouse_name,
        available_stock: 0,
        display_name: `${selectedBatch.warehouse_name} (正在获取库存...)`
      }]
    }
  }

  // 手动创建时，需要根据批次筛选仓库
  if (item.available_batches) {
    const selectedBatch = item.available_batches.find((batch: any) => batch.batch_no === item.batch_no)
    if (selectedBatch && selectedBatch.warehouse_id) {
      // 检查缓存中是否有该批次的库存信息
      const cacheKey = `${item.product_id}_${item.batch_no}_${selectedBatch.warehouse_id}`
      if (batchStockCache.value[cacheKey]) {
        const stockInfo = batchStockCache.value[cacheKey]
        return [{
          id: selectedBatch.warehouse_id,
          name: selectedBatch.warehouse_name,
          available_stock: stockInfo.available_stock || 0,
          display_name: `${selectedBatch.warehouse_name} (批次库存: ${stockInfo.available_stock || 0})`
        }]
      }

      // 如果缓存中没有，使用商品仓库信息
      const allWarehouses = productWarehouses.value[item.product_id] || []
      const matchedWarehouses = allWarehouses.filter(warehouse =>
        warehouse.id === selectedBatch.warehouse_id
      )

      // 异步获取批次库存信息并缓存
      fetchBatchStockInfo(item.product_id, item.batch_no, selectedBatch.warehouse_id)

      return matchedWarehouses.length > 0 ? matchedWarehouses : [{
        id: selectedBatch.warehouse_id,
        name: selectedBatch.warehouse_name,
        display_name: `${selectedBatch.warehouse_name} (正在获取库存...)`
      }]
    }
  }

  // 如果没有批次信息，返回该商品的所有仓库
  return productWarehouses.value[item.product_id] || []
}

// 异步获取批次库存信息并缓存
const fetchBatchStockInfo = async (productId: number, batchNo: string, warehouseId: number) => {
  const cacheKey = `${productId}_${batchNo}_${warehouseId}`

  // 如果已经在获取中，避免重复请求
  if (batchStockCache.value[cacheKey + '_loading']) {
    return
  }

  // 标记正在加载
  batchStockCache.value[cacheKey + '_loading'] = true

  try {
    // 调用API获取该商品该批次在该仓库的库存信息
    const stockInfo = await purchaseReturnApi.getBatchStockInWarehouse(
      productId,
      batchNo,
      warehouseId
    )

    // 缓存库存信息
    batchStockCache.value[cacheKey] = stockInfo

    console.log(`✅ 获取批次库存成功: 商品${productId}, 批次${batchNo}, 仓库${warehouseId}, 库存${stockInfo.available_stock}`)

  } catch (error) {
    console.error('获取批次库存信息失败:', error)
    // 缓存错误信息
    batchStockCache.value[cacheKey] = { available_stock: 0 }
  } finally {
    // 清除加载标记
    delete batchStockCache.value[cacheKey + '_loading']
  }
}

// 仓库选择变更处理
const onWarehouseChange = (item: any) => {
  // 仓库变更时可以添加额外的逻辑
  console.log('仓库已变更:', item.warehouse_id)
}

const saveReturn = async () => {
  if (!returnFormRef.value) return

  try {
    await returnFormRef.value.validate()

    if (returnForm.items.length === 0) {
      ElMessage.warning('请至少添加一个退货商品')
      return
    }

    // 验证商品明细
    for (let i = 0; i < returnForm.items.length; i++) {
      const item = returnForm.items[i]

      if (!item.product_id) {
        ElMessage.warning(`第${i + 1}行：请选择商品`)
        return
      }

      if (!item.warehouse_id) {
        ElMessage.warning(`第${i + 1}行：请选择退货仓库`)
        return
      }

      if (!item.return_quantity || item.return_quantity <= 0) {
        ElMessage.warning(`第${i + 1}行：退货数量必须大于0`)
        return
      }
    }

    saving.value = true

    if (isEditing.value) {
      // 更新现有退货单
      const updateData = {
        receipt_no: returnForm.return_type === 'from_receipt' ? returnForm.receipt_no : undefined,
        supplier_id: returnForm.supplier_id!,
        return_date: returnForm.return_date || undefined,
        reason: returnForm.reason,
        remark: returnForm.remark,
        items: returnForm.items
      }

      await purchaseReturnApi.updatePurchaseReturn(returnForm.id!, updateData)
      ElMessage.success('退货单更新成功')
    } else {
      // 新增退货单
      const createData = {
        receipt_no: returnForm.return_type === 'from_receipt' ? returnForm.receipt_no : undefined,
        supplier_id: returnForm.supplier_id!,
        return_date: returnForm.return_date || new Date().toISOString().split('T')[0],
        reason: returnForm.reason || '质量问题',
        remark: returnForm.remark || '',
        total_amount: returnForm.total_amount || 0,
        items: returnForm.items.map(item => ({
          product_id: item.product_id,
          product_name: item.product_name,
          product_sku: item.product_sku || '',
          batch_no: item.batch_no || '',
          warehouse_id: item.warehouse_id,
          receipt_line_number: item.line_number || null,
          return_quantity: Number(item.return_quantity) || 0,
          unit_price: Number(item.unit_price) || 0,
          total_price: Number(item.total_price) || 0,
          quality_issue: item.quality_issue || ''
        }))
      }

      console.log('创建退货单数据:', createData)
      console.log('退货单明细数据:', createData.items)
      try {
        await purchaseReturnApi.createPurchaseReturn(createData)
        ElMessage.success('退货单创建成功')
      } catch (error: any) {
        console.error('创建退货单失败:', error)
        if (error.response?.data?.detail) {
          ElMessage.error(`创建失败: ${error.response.data.detail}`)
        } else {
          ElMessage.error('创建退货单失败，请检查输入信息')
        }
        throw error
      }
    }

    // 重新获取数据
    await fetchPurchaseReturns()

    showCreateDialog.value = false
    resetReturnForm()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetReturnForm = () => {
  isEditing.value = false
  Object.assign(returnForm, {
    id: null,
    return_type: 'manual',
    return_no: '',
    supplier_id: null,
    receipt_no: '',
    return_date: '',
    reason: '',
    remark: '',
    total_amount: 0,
    status: 'draft',
    items: []
  })
  if (returnFormRef.value) {
    returnFormRef.value.clearValidate()
  }
}

// 获取可用的收货单
const fetchAvailableReceipts = async () => {
  loadingReceipts.value = true
  try {
    // 获取已完成的收货单
    const response = await purchaseReturnApi.getAvailableReceipts()
    availableReceipts.value = response || []
  } catch (error) {
    console.error('获取收货单失败:', error)
    ElMessage.error('获取收货单失败')
  } finally {
    loadingReceipts.value = false
  }
}

// 获取可用的商品
const fetchAvailableProducts = async () => {
  try {
    const response = await productApi.getProducts({
      page: 1,
      page_size: 100
    })
    availableProducts.value = response.items || []
  } catch (error) {
    console.error('获取商品失败:', error)
    ElMessage.error('获取商品失败')
  }
}

// 辅助方法
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝',
    returned: '已退货',  // 最终状态
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: 'info',
    submitted: 'warning',
    approved: 'success',
    rejected: 'danger',
    returned: 'success',  // 最终状态
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchPurchaseReturns()
  fetchSuppliers()
  fetchAvailableProducts()
  fetchWarehouses()
})
</script>

<style scoped>
/* 自定义表格样式 */
/* 表格包装器 */
.table-wrapper {
  overflow-x: auto;
  overflow-y: hidden; /* 防止垂直滚动条 */
  width: 100%;
  margin-top: 10px;
  padding: 0 4px;
}

.custom-table {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  min-width: 1200px; /* 设置最小宽度确保表格不会过度压缩 */
}

/* 确保对话框内容不产生额外的滚动条 */
:deep(.el-dialog__body) {
  overflow-x: hidden !important;
  padding: 20px 20px 0 20px;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden; /* 强制隐藏横向滚动条，由父容器处理 */
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: #f5f7fa;
}

.header-cell,
.table-cell {
  padding: 8px 12px;
  border-right: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  min-height: 40px;
}

.header-cell:last-child,
.table-cell:last-child {
  border-right: none;
}

.header-cell {
  font-weight: 600;
  color: #909399;
  background-color: #f5f7fa;
}

/* 列宽度定义 */
.product-name {
  width: 200px;
  min-width: 200px;
}

.sku {
  width: 120px;
  min-width: 120px;
}

.batch-no-wide {
  width: 180px;
  min-width: 180px;
}

.warehouse {
  width: 200px;
  min-width: 200px;
}

.quantity {
  width: 120px;
  min-width: 120px;
}

.unit-price {
  width: 120px;
  min-width: 120px;
}

.total-price {
  width: 120px;
  min-width: 120px;
}

.quality-issue {
  width: 200px;
  min-width: 200px;
}

.actions {
  width: 100px;
  min-width: 100px;
}

/* 数量信息样式 */
.quantity-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 14px;
}

.returned-info {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 2px;
}

/* 提示信息样式 */
.receipt-mode-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
}

.receipt-mode-tip .el-icon {
  color: #409eff;
}

/* 退货商品区域样式 */
.return-items {
  width: 100%;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

/* 页面整体样式 */
.purchase-return-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.page-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 退货单概览 */
.return-overview {
  margin-bottom: 12px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.overview-icon {
  padding: 12px;
  border-radius: 12px;
  flex-shrink: 0;
}

.overview-icon.total {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.pending {
  background: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.overview-icon.processing {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.overview-icon.completed {
  background: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 退货单列表卡片 */
.return-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.return-list-card .el-card__header {
  padding: 14px 20px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 金额样式 */
.amount {
  font-weight: 600;
  color: #E6A23C;
}

/* 卡片视图 */
.card-view {
  margin-top: 20px;
}

.return-card {
  margin-bottom: 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.return-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.return-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.return-card .card-header h3 {
  margin: 0;
  color: #303133;
}

.return-card .card-content {
  margin-bottom: 16px;
}

.return-card .card-content p {
  margin: 8px 0;
  color: #606266;
}

.return-card .card-actions {
  display: flex;
  gap: 8px;
}

/* 表格操作 */
.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
}

/* 文本样式 */
.text-gray {
  color: #909399;
  font-style: italic;
}

/* 分页 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .purchase-return-view {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .return-overview .el-col {
    margin-bottom: 12px;
  }

  .overview-content {
    padding: 16px;
  }

  .search-card .el-form {
    flex-direction: column;
  }

  .search-card .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;
  }

  .card-view .el-col {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-left h2 {
    font-size: 24px;
  }

  .overview-value {
    font-size: 20px;
  }

  .overview-content {
    gap: 12px;
  }

  .overview-icon {
    padding: 8px;
  }
}
</style>
