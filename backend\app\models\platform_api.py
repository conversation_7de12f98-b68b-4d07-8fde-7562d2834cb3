"""
电商平台API配置数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Enum
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class APIType(str, enum.Enum):
    """API类型枚举"""
    INVENTORY_QUERY = "inventory_query"  # 库存查询
    INVENTORY_UPDATE = "inventory_update"  # 库存修改
    ORDER_LIST = "order_list"  # 销售订单列表
    ORDER_DETAIL = "order_detail"  # 销售订单详情
    PRODUCT_LIST = "product_list"  # 商品列表
    PRODUCT_DETAIL = "product_detail"  # 商品详情


class PlatformType(str, enum.Enum):
    """平台类型枚举"""
    TAOBAO = "taobao"  # 淘宝
    TMALL = "tmall"  # 天猫
    JD = "jd"  # 京东
    PDD = "pdd"  # 拼多多
    DOUYIN = "douyin"  # 抖音
    KUAISHOU = "kuaishou"  # 快手
    WECHAT = "wechat"  # 微信小程序
    CUSTOM = "custom"  # 自定义平台


class PlatformAPI(Base):
    """电商平台API配置表"""
    __tablename__ = "platform_apis"

    id = Column(Integer, primary_key=True, index=True)
    platform_type = Column(Enum(PlatformType), nullable=False, comment="平台类型")
    platform_name = Column(String(100), nullable=False, comment="平台名称")
    api_type = Column(Enum(APIType), nullable=False, comment="API类型")
    api_name = Column(String(100), nullable=False, comment="API名称")
    api_url = Column(String(500), nullable=False, comment="API地址")
    api_method = Column(String(10), default="GET", comment="请求方法")
    api_headers = Column(Text, comment="请求头配置(JSON格式)")
    api_params = Column(Text, comment="请求参数配置(JSON格式)")
    api_auth = Column(Text, comment="认证配置(JSON格式)")
    description = Column(Text, comment="API描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<PlatformAPI(platform={self.platform_name}, api={self.api_name})>"
