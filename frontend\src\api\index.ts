import axios from 'axios'

/**
 * HTTP请求客户端配置
 * 统一的API请求客户端，用于所有API调用
 */

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  async (error) => {
    console.error('API Error:', error)
    if (error.response) {
      console.error('Error Response:', error.response.data)
      console.error('Error Status:', error.response.status)

      // 处理401认证错误
      if (error.response.status === 401) {
        const originalRequest = error.config

        // 避免无限循环，如果是刷新token的请求失败，直接跳转登录
        if (originalRequest.url?.includes('/api/auth/refresh')) {
          console.warn('刷新token失败，请重新登录')
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          if (typeof window !== 'undefined') {
            window.location.href = '/login'
          }
          return Promise.reject(error)
        }

        // 尝试刷新token
        try {
          const token = localStorage.getItem('token')
          if (token) {
            const refreshResponse = await api.post('/api/auth/refresh')
            if (refreshResponse.success) {
              // 更新token
              localStorage.setItem('token', refreshResponse.token)
              api.defaults.headers.common['Authorization'] = `Bearer ${refreshResponse.token}`
              originalRequest.headers.Authorization = `Bearer ${refreshResponse.token}`

              // 重试原始请求
              return api(originalRequest)
            }
          }
        } catch (refreshError) {
          console.warn('自动刷新token失败:', refreshError)
        }

        // 刷新失败，清除token并跳转登录
        console.warn('认证失效，请重新登录')
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        if (typeof window !== 'undefined') {
          window.location.href = '/login'
        }
      }
    }
    return Promise.reject(error)
  }
)

/**
 * 导出配置
 * - api: 主要的HTTP客户端实例
 * - request: api的别名，用于向后兼容
 * - default: 默认导出api实例
 */
export { api, api as request }
export default api
