import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { productApi, type Product } from '../api/products'

export const useProductStore = defineStore('products', () => {
  // 状态
  const products = ref<Product[]>([])
  const currentProduct = ref<Product | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const productsByCategory = computed(() => {
    const categories: Record<string, Product[]> = {}
    products.value.forEach(product => {
      if (!categories[product.category]) {
        categories[product.category] = []
      }
      categories[product.category].push(product)
    })
    return categories
  })

  // 操作方法
  const fetchProducts = async (params?: { skip?: number; limit?: number; category?: string }) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.getProducts(params)
      products.value = data.items || []
    } catch (err) {
      error.value = '获取商品列表失败'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  const fetchProduct = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.getProduct(id)
      currentProduct.value = data
      return data
    } catch (err) {
      error.value = '获取商品详情失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const createProduct = async (productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.createProduct(productData)
      products.value.push(data)
      return data
    } catch (err) {
      error.value = '创建商品失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const updateProduct = async (id: number, productData: Partial<Product>) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.updateProduct(id, productData)
      const index = products.value.findIndex(p => p.id === id)
      if (index !== -1) {
        products.value[index] = data
      }
      if (currentProduct.value?.id === id) {
        currentProduct.value = data
      }
      return data
    } catch (err) {
      error.value = '更新商品失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const deleteProduct = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      await productApi.deleteProduct(id)
      products.value = products.value.filter(p => p.id !== id)
      if (currentProduct.value?.id === id) {
        currentProduct.value = null
      }
      return true
    } catch (err) {
      error.value = '删除商品失败'
      console.error(err)
      return false
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    products,
    currentProduct,
    loading,
    error,
    
    // 计算属性
    productsByCategory,
    
    // 方法
    fetchProducts,
    fetchProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    clearError
  }
})