"""
电商平台API配置服务
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.models.platform_api import PlatformAPI, APIType, PlatformType
from app.schemas.platform_api import (
    PlatformAPICreate, 
    PlatformAPIUpdate, 
    PlatformAPIQuery,
    APITypeInfo,
    PlatformTypeInfo,
    PlatformAPIOptions
)
import math


class PlatformAPIService:
    """电商平台API配置服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_platform_apis(self, query: PlatformAPIQuery) -> tuple[List[PlatformAPI], int]:
        """获取电商平台API配置列表"""
        # 构建查询条件
        filters = []
        
        if query.platform_type:
            filters.append(PlatformAPI.platform_type == query.platform_type)
        
        if query.api_type:
            filters.append(PlatformAPI.api_type == query.api_type)
        
        if query.platform_name:
            filters.append(PlatformAPI.platform_name.ilike(f"%{query.platform_name}%"))
        
        if query.api_name:
            filters.append(PlatformAPI.api_name.ilike(f"%{query.api_name}%"))
        
        if query.is_active is not None:
            filters.append(PlatformAPI.is_active == query.is_active)

        # 构建查询
        query_obj = self.db.query(PlatformAPI)
        if filters:
            query_obj = query_obj.filter(and_(*filters))

        # 获取总数
        total = query_obj.count()

        # 分页查询
        offset = (query.page - 1) * query.page_size
        items = query_obj.order_by(PlatformAPI.created_at.desc()).offset(offset).limit(query.page_size).all()

        return items, total

    def get_platform_api(self, api_id: int) -> Optional[PlatformAPI]:
        """获取单个电商平台API配置"""
        return self.db.query(PlatformAPI).filter(PlatformAPI.id == api_id).first()

    def create_platform_api(self, api_data: PlatformAPICreate) -> PlatformAPI:
        """创建电商平台API配置"""
        # 检查是否已存在相同的配置
        existing = self.db.query(PlatformAPI).filter(
            and_(
                PlatformAPI.platform_type == api_data.platform_type,
                PlatformAPI.api_type == api_data.api_type,
                PlatformAPI.platform_name == api_data.platform_name
            )
        ).first()
        
        if existing:
            raise ValueError(f"平台 {api_data.platform_name} 的 {api_data.api_type.value} API配置已存在")

        # 创建新配置
        db_api = PlatformAPI(**api_data.model_dump())
        self.db.add(db_api)
        self.db.commit()
        self.db.refresh(db_api)
        return db_api

    def update_platform_api(self, api_id: int, api_data: PlatformAPIUpdate) -> Optional[PlatformAPI]:
        """更新电商平台API配置"""
        db_api = self.get_platform_api(api_id)
        if not db_api:
            return None

        # 如果更新了关键字段，检查是否会产生重复
        if any([api_data.platform_type, api_data.api_type, api_data.platform_name]):
            platform_type = api_data.platform_type or db_api.platform_type
            api_type = api_data.api_type or db_api.api_type
            platform_name = api_data.platform_name or db_api.platform_name
            
            existing = self.db.query(PlatformAPI).filter(
                and_(
                    PlatformAPI.id != api_id,
                    PlatformAPI.platform_type == platform_type,
                    PlatformAPI.api_type == api_type,
                    PlatformAPI.platform_name == platform_name
                )
            ).first()
            
            if existing:
                raise ValueError(f"平台 {platform_name} 的 {api_type.value} API配置已存在")

        # 更新字段
        update_data = api_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_api, field, value)

        self.db.commit()
        self.db.refresh(db_api)
        return db_api

    def delete_platform_api(self, api_id: int) -> bool:
        """删除电商平台API配置"""
        db_api = self.get_platform_api(api_id)
        if not db_api:
            return False

        self.db.delete(db_api)
        self.db.commit()
        return True

    def get_platform_api_options(self) -> PlatformAPIOptions:
        """获取电商平台API配置选项"""
        # API类型选项
        api_types = [
            APITypeInfo(
                value=APIType.INVENTORY_QUERY.value,
                label="库存查询",
                description="查询商品库存信息的API"
            ),
            APITypeInfo(
                value=APIType.INVENTORY_UPDATE.value,
                label="库存修改",
                description="修改商品库存的API"
            ),
            APITypeInfo(
                value=APIType.ORDER_LIST.value,
                label="订单列表",
                description="获取销售订单列表的API"
            ),
            APITypeInfo(
                value=APIType.ORDER_DETAIL.value,
                label="订单详情",
                description="获取销售订单详情的API"
            ),
            APITypeInfo(
                value=APIType.PRODUCT_LIST.value,
                label="商品列表",
                description="获取商品列表的API"
            ),
            APITypeInfo(
                value=APIType.PRODUCT_DETAIL.value,
                label="商品详情",
                description="获取商品详情的API"
            )
        ]

        # 平台类型选项
        platform_types = [
            PlatformTypeInfo(
                value=PlatformType.TAOBAO.value,
                label="淘宝",
                description="淘宝平台"
            ),
            PlatformTypeInfo(
                value=PlatformType.TMALL.value,
                label="天猫",
                description="天猫平台"
            ),
            PlatformTypeInfo(
                value=PlatformType.JD.value,
                label="京东",
                description="京东平台"
            ),
            PlatformTypeInfo(
                value=PlatformType.PDD.value,
                label="拼多多",
                description="拼多多平台"
            ),
            PlatformTypeInfo(
                value=PlatformType.DOUYIN.value,
                label="抖音",
                description="抖音电商平台"
            ),
            PlatformTypeInfo(
                value=PlatformType.KUAISHOU.value,
                label="快手",
                description="快手电商平台"
            ),
            PlatformTypeInfo(
                value=PlatformType.WECHAT.value,
                label="微信小程序",
                description="微信小程序商城"
            ),
            PlatformTypeInfo(
                value=PlatformType.CUSTOM.value,
                label="自定义平台",
                description="自定义电商平台"
            )
        ]

        # HTTP方法选项
        api_methods = ["GET", "POST", "PUT", "DELETE", "PATCH"]

        return PlatformAPIOptions(
            api_types=api_types,
            platform_types=platform_types,
            api_methods=api_methods
        )
