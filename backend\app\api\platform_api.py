"""
电商平台API配置管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.core.database import get_db
from app.services.platform_api_service import PlatformAPIService
from app.schemas.platform_api import (
    PlatformAPI,
    PlatformAPICreate,
    PlatformAPIUpdate,
    PlatformAPIListResponse,
    PlatformAPIQuery,
    PlatformAPIOptions
)
from app.models.platform_api import APIType, PlatformType
import math

router = APIRouter()


@router.get("/options", response_model=PlatformAPIOptions)
async def get_platform_api_options(db: Session = Depends(get_db)):
    """获取电商平台API配置选项"""
    service = PlatformAPIService(db)
    return service.get_platform_api_options()


@router.get("/", response_model=PlatformAPIListResponse)
async def get_platform_apis(
    platform_type: Optional[PlatformType] = Query(None, description="平台类型"),
    api_type: Optional[APIType] = Query(None, description="API类型"),
    platform_name: Optional[str] = Query(None, description="平台名称"),
    api_name: Optional[str] = Query(None, description="API名称"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取电商平台API配置列表"""
    query = PlatformAPIQuery(
        platform_type=platform_type,
        api_type=api_type,
        platform_name=platform_name,
        api_name=api_name,
        is_active=is_active,
        page=page,
        page_size=page_size
    )
    
    service = PlatformAPIService(db)
    items, total = service.get_platform_apis(query)
    
    total_pages = math.ceil(total / page_size) if total > 0 else 0
    
    return PlatformAPIListResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages
    )


@router.get("/{api_id}", response_model=PlatformAPI)
async def get_platform_api(api_id: int, db: Session = Depends(get_db)):
    """获取单个电商平台API配置"""
    service = PlatformAPIService(db)
    api_config = service.get_platform_api(api_id)
    if not api_config:
        raise HTTPException(status_code=404, detail="API配置不存在")
    return api_config


@router.post("/", response_model=PlatformAPI)
async def create_platform_api(
    api_data: PlatformAPICreate,
    db: Session = Depends(get_db)
):
    """创建电商平台API配置"""
    service = PlatformAPIService(db)
    try:
        return service.create_platform_api(api_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{api_id}", response_model=PlatformAPI)
async def update_platform_api(
    api_id: int,
    api_data: PlatformAPIUpdate,
    db: Session = Depends(get_db)
):
    """更新电商平台API配置"""
    service = PlatformAPIService(db)
    try:
        updated_api = service.update_platform_api(api_id, api_data)
        if not updated_api:
            raise HTTPException(status_code=404, detail="API配置不存在")
        return updated_api
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{api_id}")
async def delete_platform_api(api_id: int, db: Session = Depends(get_db)):
    """删除电商平台API配置"""
    service = PlatformAPIService(db)
    success = service.delete_platform_api(api_id)
    if not success:
        raise HTTPException(status_code=404, detail="API配置不存在")
    return {"message": "API配置删除成功"}


@router.post("/{api_id}/toggle")
async def toggle_platform_api_status(api_id: int, db: Session = Depends(get_db)):
    """切换电商平台API配置启用状态"""
    service = PlatformAPIService(db)
    api_config = service.get_platform_api(api_id)
    if not api_config:
        raise HTTPException(status_code=404, detail="API配置不存在")
    
    # 切换状态
    update_data = PlatformAPIUpdate(is_active=not api_config.is_active)
    updated_api = service.update_platform_api(api_id, update_data)
    
    return {
        "message": f"API配置已{'启用' if updated_api.is_active else '禁用'}",
        "is_active": updated_api.is_active
    }
