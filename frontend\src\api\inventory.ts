/**
 * 库存管理API - 包含库存管理、库存调拨
 */

import api from './index'

// ==================== 库存管理相关类型定义 ====================
export interface InventoryItem {
  id: number
  product_id: number
  warehouse_id: number
  current_stock: number
  reserved_stock: number
  available_stock: number
  min_stock: number
  max_stock: number
  safety_stock: number
  average_cost: number
  last_cost: number
  location?: string
  zone?: string
  batch_no?: string
  production_date?: string
  expiry_date?: string
  status: 'normal' | 'locked' | 'damaged'
  is_active: boolean
  product_name: string
  product_sku: string
  product_category?: string
  product_image?: string
  warehouse_name: string
  created_at: string
  updated_at?: string
  last_in_date?: string
  last_out_date?: string
}

export interface InventoryStats {
  total_products: number
  total_warehouses: number
  normal_stock: number
  low_stock: number
  out_of_stock: number
  total_stock_value: number
  average_stock_level: number
}

export interface InventoryQuery {
  product_name?: string
  product_sku?: string
  product_category?: string
  warehouse_id?: number
  batch_no?: string
  production_date_start?: string
  production_date_end?: string
  expiry_date_start?: string
  expiry_date_end?: string
  is_expired?: boolean
  is_near_expiry?: boolean
  status?: string
  stock_status?: 'sufficient' | 'low' | 'out'
  exclude_zero_stock?: boolean
}

export interface InventoryAdjustment {
  inventory_id: number
  adjustment_type: 'in' | 'out' | 'adjust'
  quantity: number
  reason: string
  remark?: string
  operator: string
}

export interface InventoryTransaction {
  id: number
  inventory_id: number
  transaction_type: 'in' | 'out' | 'adjust' | 'transfer'
  quantity: number
  before_quantity: number
  after_quantity: number
  reference_type?: string
  reference_id?: number
  reference_no?: string
  operator: string
  reason?: string
  remark?: string
  product_name: string
  product_sku: string
  warehouse_name: string
  created_at: string
}

export interface Warehouse {
  id: number
  name: string
  address: string
  total_capacity_m3: number
  used_capacity_m3: number
  available_capacity_m3: number
  utilization_rate: number
  inventory_count: number
  latitude?: number
  longitude?: number
  operating_hours?: any
  contact_info?: any
  is_active: boolean
  status: string
  created_at: string
  updated_at?: string
}

export interface PaginatedInventoryResponse {
  items: InventoryItem[]
  total: number
  page: number
  page_size: number
  pages: number
}

export interface PaginatedTransactionResponse {
  items: InventoryTransaction[]
  total: number
  page: number
  page_size: number
  pages: number
}

// ==================== 库存调拨相关类型定义 ====================

// 简化的仓库接口（用于调拨单选择）
export interface SimpleWarehouse {
  id: number
  name: string
  address: string
  status: string
  is_active: boolean
}

// 库存调拨单状态枚举
export enum InventoryTransferStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  TRANSFERRED = 'transferred',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 库存调拨单明细接口
export interface InventoryTransferItem {
  id?: number
  inventory_transfer_id?: number
  product_id: number
  product_name: string
  product_sku?: string
  transfer_quantity: number
  available_quantity?: number
  batch_no?: string
  created_at?: string
  updated_at?: string
}

// 库存调拨单接口
export interface InventoryTransfer {
  id?: number
  transfer_no: string
  from_warehouse_id: number
  to_warehouse_id: number
  transfer_date: string
  reason: string
  total_quantity: number
  status: InventoryTransferStatus
  remark?: string

  // 审核信息
  submitted_at?: string
  submitted_by?: string
  approved_at?: string
  approved_by?: string
  approval_note?: string

  // 调拨信息
  transferred_at?: string
  transferred_by?: string

  // 时间戳
  created_at?: string
  updated_at?: string
  created_by?: string
  updated_by?: string

  // 关联信息
  from_warehouse_name?: string
  to_warehouse_name?: string

  // 明细
  items: InventoryTransferItem[]
}

// 创建库存调拨单数据
export interface InventoryTransferCreate {
  transfer_no?: string
  from_warehouse_id: number
  to_warehouse_id: number
  transfer_date: string
  reason: string
  total_quantity: number
  remark?: string
  items: Omit<InventoryTransferItem, 'id' | 'inventory_transfer_id' | 'created_at' | 'updated_at'>[]
}

// 更新库存调拨单数据
export interface InventoryTransferUpdate {
  transfer_no?: string
  from_warehouse_id?: number
  to_warehouse_id?: number
  transfer_date?: string
  reason?: string
  total_quantity?: number
  remark?: string
  items?: Omit<InventoryTransferItem, 'id' | 'inventory_transfer_id' | 'created_at' | 'updated_at'>[]
}

// 状态更新数据
export interface InventoryTransferStatusUpdate {
  status: InventoryTransferStatus
  note?: string
}

// 库存调拨单统计数据接口
export interface InventoryTransferStats {
  total_transfers: number
  draft_transfers: number
  submitted_transfers: number
  approved_transfers: number
  transferred_transfers: number
  completed_transfers: number
  total_quantity: number
}

// 库存调拨单查询参数接口
export interface InventoryTransferQuery {
  transfer_no?: string
  from_warehouse_id?: number
  to_warehouse_id?: number
  status?: InventoryTransferStatus
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}







// ==================== API接口 ====================

// 库存管理API
export const inventoryApi = {
  /**
   * 获取库存列表
   */
  async getInventoryList(params: {
    page?: number
    page_size?: number
    product_name?: string
    product_sku?: string
    product_category?: string
    warehouse_id?: number
    status?: string
    stock_status?: string
  } = {}): Promise<PaginatedInventoryResponse> {
    return await api.get('/api/inventory/', { params })
  },

  /**
   * 获取库存统计
   */
  async getInventoryStats(): Promise<InventoryStats> {
    return await api.get('/api/inventory/stats')
  },

  /**
   * 获取仓库列表
   */
  async getWarehouses(): Promise<Warehouse[]> {
    return await api.get('/api/inventory/warehouses')
  },

  /**
   * 获取单个库存记录
   */
  async getInventory(inventoryId: number): Promise<InventoryItem> {
    return await api.get(`/api/inventory/${inventoryId}`)
  },

  /**
   * 创建库存记录
   */
  async createInventory(data: {
    product_id: number
    warehouse_id: number
    current_stock?: number
    reserved_stock?: number
    min_stock?: number
    max_stock?: number
    safety_stock?: number
    average_cost?: number
    last_cost?: number
    location?: string
    zone?: string
    status?: string
    is_active?: boolean
  }): Promise<InventoryItem> {
    return await api.post('/api/inventory/', data)
  },

  /**
   * 更新库存记录
   */
  async updateInventory(inventoryId: number, data: {
    current_stock?: number
    reserved_stock?: number
    min_stock?: number
    max_stock?: number
    safety_stock?: number
    average_cost?: number
    last_cost?: number
    location?: string
    zone?: string
    status?: string
    is_active?: boolean
  }): Promise<InventoryItem> {
    return await api.put(`/api/inventory/${inventoryId}`, data)
  },

  /**
   * 调整库存
   */
  async adjustInventory(adjustment: InventoryAdjustment): Promise<InventoryItem> {
    return await api.post('/api/inventory/adjust', adjustment)
  },

  /**
   * 获取库存变动记录
   */
  async getInventoryTransactions(params: {
    inventory_id?: number
    page?: number
    page_size?: number
  } = {}): Promise<PaginatedTransactionResponse> {
    return await api.get('/api/inventory/transactions/', { params })
  }
}

// 商品批次信息接口
export interface ProductBatch {
  batch_no?: string
  production_date?: string
  expiry_date?: string
  total_stock: number
  available_stock: number
  warehouses?: {
    warehouse_id: number
    warehouse_name: string
    stock: number
  }[]
}

// 可用商品接口
export interface AvailableProduct {
  id: number
  name: string
  sku: string
  category?: string
  total_stock: number
}

// 库存调拨API
export const inventoryTransferApi = {
  // 获取活跃仓库列表（用于调拨单选择）
  async getActiveWarehouses(): Promise<SimpleWarehouse[]> {
    return await api.get('/api/inventory/transfers/active-warehouses')
  },

  // 获取有库存的仓库列表（用于调拨单调出仓库选择）
  async getWarehousesWithStock(): Promise<SimpleWarehouse[]> {
    return await api.get('/api/inventory/transfers/warehouses-with-stock')
  },

  // 获取商品批次信息（指定仓库）
  async getProductBatches(productId: number, warehouseId: number): Promise<ProductBatch[]> {
    return await api.get('/api/inventory/transfers/product-batches', {
      params: { product_id: productId, warehouse_id: warehouseId }
    })
  },

  // 获取商品所有批次信息（跨所有仓库汇总）
  async getProductAllBatches(productId: number): Promise<ProductBatch[]> {
    return await api.get('/api/inventory/transfers/product-all-batches', {
      params: { product_id: productId }
    })
  },

  // 获取可用商品列表
  async getAvailableProducts(warehouseId?: number): Promise<AvailableProduct[]> {
    return await api.get('/api/inventory/transfers/available-products', {
      params: warehouseId ? { warehouse_id: warehouseId } : {}
    })
  },

  // 获取统计信息
  async getStats(): Promise<InventoryTransferStats> {
    return await api.get('/api/inventory/transfers/stats')
  },

  // 获取库存调拨单列表
  async getInventoryTransfers(params?: InventoryTransferQuery): Promise<InventoryTransfer[]> {
    return await api.get('/api/inventory/transfers/', { params })
  },

  // 获取库存调拨单详情
  async getInventoryTransfer(id: number): Promise<InventoryTransfer> {
    return await api.get(`/api/inventory/transfers/${id}`)
  },

  // 创建库存调拨单
  async createInventoryTransfer(data: InventoryTransferCreate): Promise<InventoryTransfer> {
    return await api.post('/api/inventory/transfers/', data)
  },

  // 更新库存调拨单
  async updateInventoryTransfer(id: number, data: InventoryTransferUpdate): Promise<InventoryTransfer> {
    return await api.put(`/api/inventory/transfers/${id}`, data)
  },

  // 删除库存调拨单
  async deleteInventoryTransfer(id: number): Promise<{ message: string }> {
    return await api.delete(`/api/inventory/transfers/${id}`)
  },

  // 提交库存调拨单
  async submitInventoryTransfer(id: number): Promise<{ message: string }> {
    return await api.post(`/api/inventory/transfers/${id}/submit`)
  },

  // 审核库存调拨单
  async approveInventoryTransfer(id: number, data: InventoryTransferStatusUpdate): Promise<{ message: string }> {
    return await api.post(`/api/inventory/transfers/${id}/approve`, data)
  },

  // 确认调拨
  async transferInventoryTransfer(id: number): Promise<{ message: string }> {
    return await api.post(`/api/inventory/transfers/${id}/transfer`)
  }
}



// 默认导出库存管理API（保持向后兼容）
export default inventoryApi
