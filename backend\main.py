from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import products, logistics, auth, suppliers, customers, purchase, inventory, sales
from app.api.warehouses import router as warehouses_router
from app.api.platform_api import router as platform_api_router
from app.core.config import config
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)

app = FastAPI(
    title="电子商务决策系统 API",
    description="提供选品决策和物流配送功能的API服务",
    version="1.0.0"
)

# 应用启动时执行数据库迁移
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    logging.info("🚀 应用启动中...")
    logging.info("✅ 应用启动完成")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ORIGINS,  # 使用配置文件中的CORS设置
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["用户认证"])
app.include_router(products.router, prefix="/api/products", tags=["选品决策"])
app.include_router(suppliers.router, prefix="/api/suppliers", tags=["供应商管理"])
app.include_router(customers.router, prefix="/api/customers", tags=["客户管理"])
app.include_router(purchase.router, prefix="/api/purchase", tags=["采购管理"])
app.include_router(sales.router, prefix="/api/sales", tags=["销售管理"])
app.include_router(inventory.router, prefix="/api/inventory", tags=["库存管理"])
app.include_router(warehouses_router, prefix="/api/warehouses", tags=["仓库管理"])
app.include_router(platform_api_router, prefix="/api/platform-apis", tags=["电商平台API管理"])
app.include_router(logistics.router, prefix="/api/logistics", tags=["物流配送"])

@app.get("/")
async def root():
    return {"message": "电子商务决策系统 API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
