from sqlalchemy import Column, Integer, String, Float, DateTime, Text, JSON, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True, comment="商品名称")
    sku = Column(String(100), unique=True, comment="商品SKU")
    brand = Column(String(100), comment="品牌")
    category = Column(String(100), nullable=False, index=True, comment="商品类别")
    price = Column(Float, nullable=False, comment="销售价格")
    cost = Column(Float, nullable=False, comment="成本价格")
    stock = Column(Integer, default=0, comment="库存数量")
    description = Column(Text, comment="商品描述")
    specifications = Column(Text, comment="规格参数")
    image = Column(String(500), comment="商品图片URL")

    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    status = Column(String(20), default="active", comment="状态: active, inactive, discontinued")

    # 销售数据 (JSON格式存储)
    sales_data = Column(JSON, comment="销售数据")

    # 市场数据
    market_trend = Column(Float, comment="市场趋势评分")
    competition_level = Column(Float, comment="竞争激烈程度")
    profit_margin = Column(Float, comment="利润率")

    # 推荐评分
    recommendation_score = Column(Float, comment="推荐评分")

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关联关系
    inventory_items = relationship("Inventory", back_populates="product")

    def __repr__(self):
        return f"<Product(id={self.id}, name='{self.name}', sku='{self.sku}')>"